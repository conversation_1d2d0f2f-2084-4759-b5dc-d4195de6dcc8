export const riskLevel2Info = (label?: number) => {
    if (label === 100) {
        return {
            type: 'danger',
            label: '高风险',
        }
    } else if (label === 50) {
        return {
            type: 'warning',
            label: '中风险',
        }
    } else if (label === 1) {
        return {
            type: 'green',
            label: '低风险',
        }
    } else if (label === 0) {
        return {
            type: 'normal',
            label: '无风险',
        }
    } else {
        return {
            type: 'normal',
            label: '无风险',
        }
    }
}

export const operationStatusLabel2Class = (val: string) => {
    if (val.includes('存续')) {
        return 'green'
    } else if (
        val.includes('迁入') ||
        val.includes('迁出') ||
        val.includes('注销') ||
        val.includes('吊销') ||
        val.includes('撤销')
    ) {
        return 'danger'
    } else {
        return 'normal'
    }
}