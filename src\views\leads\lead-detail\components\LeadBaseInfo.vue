<script lang="ts" setup>
import type { ILeadData } from '@/types/lead'
import moment from 'moment'

defineProps<{
    data: ILeadData
}>()
</script>
<template>
    <div class="flex flex-column gap-12" v-if="data">
        <div class="flex flex-column gap-8">
            <div class="font-14 color-black font-weight-500">基本信息</div>
            <div class="flex flex-column">
                <div class="font-14 color-text-grey">姓名</div>
                <div class="font-14 color-black">{{ data.name || '-' }}</div>
            </div>
            <div class="flex flex-column">
                <div class="font-14 color-text-grey">企业名称</div>
                <div class="font-14 color-black">{{ data.companyName || '-' }}</div>
            </div>
            <!-- <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">部门</div>
                    <div class="font-14 color-black">{{ data.departmentName || '-' }}</div>
                </div>
            </div> -->
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-8">
            <div class="font-14 color-black font-weight-500">联系方式</div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">电话</div>
                    <div class="font-14 color-black">
                        {{ data.contactInfo.phone || '-' }}
                    </div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">手机</div>
                    <div class="font-14 color-black">
                        {{ data.contactInfo.mobile || '-' }}
                    </div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">微信号</div>
                    <div class="font-14 color-black">-</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">QQ号</div>
                    <div class="font-14 color-black">
                        {{ data.contactInfo.qq || '-' }}
                    </div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">旺旺号</div>
                    <div class="font-14 color-black">-</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">邮箱</div>
                    <div class="font-14 color-black">
                        <div class="font-14 color-black">
                            {{ data.contactInfo.email || '-' }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">网址</div>
                    <div class="font-14 color-black">{{ data.customFields?.officialWebsite || '-' }}</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">地区</div>
                    <div class="font-14 color-black">-</div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">地址</div>
                    <div class="font-14 color-black">{{ data.customFields?.address || '-' }}</div>
                </div>
            </div>
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-8">
            <div class="font-14 color-black font-weight-500">其他信息</div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">跟进状态</div>
                    <div class="font-14 color-black">{{ data.statusStr || '-' }}</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">线索来源</div>
                    <div class="font-14 color-black">{{ data.sourceStr || '-' }}</div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">下次跟进状态</div>
                    <div class="font-14 color-black">
                        {{ data.nextFollowDate ? moment(data.nextFollowDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">备注</div>
                    <div class="font-14 color-black">{{ data.note || '-' }}</div>
                </div>
            </div>
            <div class="flex flex-column">
                <div class="font-14 color-text-grey">渠道</div>
                <div class="font-14 color-black">-</div>
            </div>
        </div>
        <div class="split-line"></div>
        <div class="flex flex-column gap-8">
            <div class="font-14 color-black font-weight-500">系统信息</div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">负责人</div>
                    <div class="font-14 color-black">{{ data.user || '-' }}</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">创建人</div>
                    <div class="font-14 color-black">{{ data.createUser || '-' }}</div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">前负责人</div>
                    <div class="font-14 color-black">{{ data.beforeUser || '-' }}</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">所属组织</div>
                    <div class="font-14 color-black">{{ data.departmentName || '-' }}</div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">前所属组织</div>
                    <div class="font-14 color-black">{{ data.lastDepartmentName || '-' }}</div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">创建时间</div>
                    <div class="font-14 color-black">
                        {{ data.createTime ? moment(data.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                </div>
            </div>
            <div class="flex flex-row">
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">更新于</div>
                    <div class="font-14 color-black">
                        {{ data.updateTime ? moment(data.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                </div>
                <div class="flex flex-column width-50">
                    <div class="font-14 color-text-grey">转客户时间</div>
                    <div class="font-14 color-black">
                        {{ data.turnCustomerDate ? moment(data.turnCustomerDate).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
