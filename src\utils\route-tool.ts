import type { RouteRecordRaw } from 'vue-router'

function getKeepAliveRouteNames(routes: RouteRecordRaw[]): string[] {
    const names: string[] = []

    routes.forEach((route) => {
        if (route.meta?.keepAlive === true && route.name) {
            names.push(route.name as string)
        }

        if (route.children) {
            names.push(...getKeepAliveRouteNames(route.children))
        }
    })

    console.log('names')
    console.log(names)

    return names
}
export { getKeepAliveRouteNames }
