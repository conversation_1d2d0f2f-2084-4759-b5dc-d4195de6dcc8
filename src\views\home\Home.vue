<script lang="ts" setup>

import { Brand, Search, Features } from './components'
import HomeSwipe from './components/HomeSwipe.vue'


</script>

<template>
    <div class="home">
        <div class="wrap">
            <div>
                <Brand />
            </div>
            <Search />
            <Features />
            <div>
                <HomeSwipe />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.home {
    box-sizing: border-box;
    height: 100%;
    overflow: scroll;
    background-color: #f2f5f8;
}

.wrap {
    padding-top: 52px;
    padding-bottom: 24px;
    background-image: url('https://7463-tcb-i5imz5tjkm83a4w-0crx07bad24e-1252168680.tcb.qcloud.la/pc-mini/images/search-company-bg.png');
    background-repeat: no-repeat;
    background-size: 100%;
}

.home-top {
    padding-left: 16px;
    padding-right: 16px;
    height: 1rem;
    margin-bottom: 4px;
}

.home-content {
    padding-left: 16px;
    padding-right: 16px;
    width: 91%;
    margin-bottom: 32px;
}

.home-spec {
    padding-left: 16px;
    padding-right: 16px;
    width: 91%;
    margin-bottom: 32px;
}
</style>
