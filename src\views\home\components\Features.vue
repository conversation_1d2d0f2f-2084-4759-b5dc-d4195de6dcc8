<script lang="ts" setup>
import IMG01 from '@/assets/hub-images/home/<USER>'
import IMG02 from '@/assets/hub-images/home/<USER>'
import IMG03 from '@/assets/hub-images/home/<USER>'
import openService from '@/service/openService'
import type { RootState } from '@/types/store'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const router = useRouter()
const store = useStore<RootState>()
const { account } = store.state.user || {}
const { user } = account || {}

const list = [
    {
        title: '产业市场',
        img: IMG02,
        action: () => {
            router.push({
                name: 'industry-market',
            })
        },
    },
    {
        title: '地图查找',
        img: IMG03,
        action: () => {
            router.push({
                name: 'mapSearch',
            })
        },
    },
    {
        title: '企业体检',
        img: IMG01,
        action: () => {
            jumpToJyhy()
        },
    },
]

const jumpToJyhy = () => {
    if (user?.id && user?.tenantId) {
        openService
            .ssoAuthentication({
                params: {
                    to: '/',
                },
                redirectUrl: import.meta.env.VITE_APP_JYHY_URL + '/stoken',
                tenantId: user.tenantId,
                userId: user.id,
            })
            .then((res) => {
                const { redirectUrl } = res
                if (redirectUrl) {
                    // 单页跳转
                    window.location.href = redirectUrl
                }
            })
    } else {
        window.open(import.meta.env.VITE_APP_JYHY_URL)
    }
}
</script>

<template>
    <div class="home-features">
        <div class="flex flex-row gap-32">
            <div
                class="flex flex-column left-right-center top-bottom-center gap-8"
                v-for="(item, index) in list"
                :key="index"
                @click="item.action"
            >
                <div class="flex w-52 h-52 border-radius-52 back-color-white left-right-center top-bottom-center">
                    <img :src="item.img" alt="" class="w-30 h-30" />
                </div>
                <div class="font-12 color-black font-weight-500">{{ item.title }}</div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.home-features {
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
}
</style>
