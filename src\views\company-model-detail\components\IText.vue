<template>
    <div class="text-container">
        <div ref="textRef" class="text-content" :class="{ collapsed: !isExpanded && showToggle }" :style="textStyle">
            <div v-html="content || '-'"></div>
            <div v-if="showToggle && !isExpanded" class="gradient-mask"></div>
            <span v-if="showToggle && !isExpanded" @click.stop="toggleExpand" class="toggle-text color-blue">
                ...更多
            </span>
        </div>

        <div v-if="showToggle && isExpanded" @click.stop="toggleExpand" class="color-blue" style="text-align: end">
            收起
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, nextTick, watch } from 'vue'

// 定义组件属性
const props = defineProps<{
    /** 显示的文本内容 */
    content: string
    /** 最大显示行数 */
    maxLines?: number
    /** 是否默认展开 */
    defaultExpanded?: boolean
}>()

// 定义事件
const emits = defineEmits<{
    (e: 'toggle', expanded: boolean): void
}>()

// DOM引用
const textRef = ref<HTMLDivElement | null>(null)

// 状态管理
const isExpanded = ref(!!props.defaultExpanded)
const isOverflow = ref(false)

// 计算属性
const maxLines = computed(() => props.maxLines || 2)

// 动态文本样式
const textStyle = computed(() => ({
    display: '-webkit-box',
    WebkitLineClamp: isExpanded.value ? 'unset' : maxLines.value,
    WebkitBoxOrient: 'vertical' as const,
    overflow: isExpanded.value ? 'visible' : 'hidden',
    textOverflow: isExpanded.value ? 'unset' : 'ellipsis',
    wordBreak: 'break-word' as const,
    position: 'relative' as const,
    maxHeight: isExpanded.value ? '9999px' : `${maxLines.value * 1.5}em`,
}))

// 使用ref而不是computed，以便我们可以直接设置它
const showToggle = ref(false)

// 监听isOverflow变化
watch(
    isOverflow,
    (newVal) => {
        console.log('isOverflow变化:', newVal)
        showToggle.value = newVal
    },
    { immediate: true }
)

// 监听展开状态变化
watch(isExpanded, () => {
    nextTick(() => {
        checkOverflow()
    })
})

// 检查文本是否溢出
const checkOverflow = () => {
    if (!textRef.value) return

    // 强制回流以获取准确高度
    const originalStyle = textRef.value.style.cssText
    textRef.value.style.cssText = 'max-height: none; -webkit-line-clamp: unset;'
    const fullHeight = textRef.value.offsetHeight

    // 计算指定行数高度
    const lineHeight = parseInt(getComputedStyle(textRef.value).lineHeight) || 20
    const maxHeight = lineHeight * maxLines.value

    // 恢复样式
    textRef.value.style.cssText = originalStyle
    // 判断是否溢出
    isOverflow.value = fullHeight > maxHeight
}

// 切换展开/收起状态
const toggleExpand = () => {
    // 获取当前高度并设置为固定值，以便动画平滑过渡
    if (textRef.value) {
        const currentHeight = textRef.value.offsetHeight
        textRef.value.style.maxHeight = `${currentHeight}px`

        // 使用setTimeout确保浏览器有时间应用上面的样式
        setTimeout(() => {
            isExpanded.value = !isExpanded.value
            emits('toggle', isExpanded.value)

            // 设置新的高度以触发动画
            if (textRef.value) {
                textRef.value.style.maxHeight = isExpanded.value ? '9999px' : `${maxLines.value * 1.5}em`
            }
        }, 10)
    } else {
        isExpanded.value = !isExpanded.value
        emits('toggle', isExpanded.value)
    }
}

// 监听内容变化
watch(
    () => props.content,
    () => {
        nextTick(() => {
            checkOverflow()
        })
    }
)

// 初始化检查
onMounted(() => {
    // 确保DOM完全渲染后再检查
    nextTick(() => {
        checkOverflow()
    })

    // 监听窗口大小变化重新计算
    window.addEventListener('resize', checkOverflow)
})

// 组件卸载时清理
onUnmounted(() => {
    window.removeEventListener('resize', checkOverflow)
})
</script>

<style scoped>
:deep(a) {
    color: #1989fa;
    text-decoration: none;
}

:deep(img) {
    max-width: 100%;
    height: auto;
}

:deep(p) {
    margin: 0.5em 0;
}
.text-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.text-content {
    position: relative;
    transition:
        max-height 0.5s ease-in-out,
        all 0.3s ease-in-out;
    overflow: hidden;
}

.collapsed {
    position: relative;
}

.gradient-mask {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 150px;
    height: 1.5em;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 1) 100%
    );
    pointer-events: none; /* 确保点击事件可以穿透到下面的文本 */
}

.toggle-text {
    cursor: pointer;
    position: absolute;
    right: 0;
    bottom: 0;
    padding-left: 5px;
    font-size: inherit;
    line-height: inherit;
    z-index: 1;
    background: transparent;
}

.toggle-text:hover {
    text-decoration: underline;
}
</style>
