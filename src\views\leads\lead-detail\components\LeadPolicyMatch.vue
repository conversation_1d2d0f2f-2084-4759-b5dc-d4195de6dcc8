<script lang="ts" setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import crmService from '@/service/crmService'
import type { IGoodsPolicyItem } from '@/types/lead'

const props = defineProps({
    companyId: {
        type: String,
        required: true,
    },
})
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const dataList = ref<IGoodsPolicyItem[] | null>(null)
const getList = () => {
    crmService.crmGoodsPolicyEntMatchRule({ companyId: props.companyId }).then((res) => {
        dataList.value = res.map((item) => {
            return {
                ...item,
                ...item.matchScore,
                name: item.name,
            }
        })
        console.log('获取政策匹配列表结果', dataList.value)
    })
}
onMounted(() => {
    getList()
})
</script>
<template>
    <van-skeleton title :row="3" v-if="!dataList" />
    <div v-if="dataList?.length === 0" class="font-14">暂无匹配项目</div>
    <div class="flex flex-column width-100">
        <div class="flex flex-column gap-8">
            <div
                class="border-radius-8 border-tag tb-padding-12 lr-padding-16"
                v-for="(item, index) in dataList"
                :key="index"
            >
                <div class="display-flex b-margin-8">
                    <div class="flex-1">
                        <div class="display-flex space-between top-bottom-center">
                            <div class="font-16 text-overflow">{{ item.name }}</div>
                            <div class="font-14 color-blue">政策匹配度：{{ item?.totalScore || 0 }}%</div>
                        </div>
                        <div class="font-14 color-text-grey">
                            <div>
                                政策类型：<span class="color-black">{{ item.childGoodsTypeStr || '-' }}</span>
                            </div>
                            <div>
                                政策标题：<span class="color-black">{{ item.name || '-' }}</span>
                            </div>
                            <div>
                                政策文号：<span class="color-black">{{ item.spu?.policyNumber || '-' }}</span>
                            </div>
                            <div>
                                政策级别：<span class="color-black">{{ item.spu?.policyLevelStr || '-' }}</span>
                            </div>
                            <div>
                                行业类别：<span class="color-black">{{ item.spu?.policyIndustryStr || '-' }}</span>
                            </div>
                            <div>
                                发文部门：<span class="color-black">{{ item.spu?.issuingDepartmentStr || '-' }}</span>
                            </div>
                            <div>
                                主题分类：<span class="color-black">{{ item.spu?.policyTopicStr || '-' }}</span>
                            </div>
                            <div>
                                申报截止日期：<span class="color-black">{{
                                    item.spu?.deadlineTime ? moment(item.spu.deadlineTime).format('YYYY-MM-DD') : '-'
                                }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
