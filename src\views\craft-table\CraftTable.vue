<template>
    <div class="home all-padding-12 border-box oh" style="background-color: #F2F5F8">
        <!-- 线索相关列表以及风险管理 -->
        <div class="display-flex flex-column gap-8">
            <div class="display-flex space-between all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <LeadMenus />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">我的代办</span>
                <MyAgent />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">企业风险监控</span>
                <companyRiskMonitor />
            </div>
            <div class="all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">销售漏斗</span>
                <div class="w-100" style="height: 8rem; width: 100%;">
                    <v-chart :option="funnelOption" style="height: 100%; width: 100%;" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { FunnelChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import LeadMenus from './components/LeadMenus.vue'
import MyAgent from './components/MyAgent.vue'
import companyRiskMonitor from './components/companyRiskMonitor.vue'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'

use([
    CanvasRenderer,
    FunnelChart,
    TitleComponent,
    TooltipComponent,
    LegendComponent
])

const funnelOption = computed(() => {
    return {
        color: ['#1966FF', '#60BCFF', '#18B4AB'],
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 10,
            textStyle: {
                fontSize: 12
            }
        },
        series: [
            {
                name: '销售漏斗',
                type: 'funnel',
                left: '10%',
                top: 60,
                width: '80%',
                height: '60%',
                minSize: '20%',
                maxSize: '100%',
                sort: 'descending',
                label: {
                    show: true,
                    position: 'inside',
                    formatter: '{c}',
                    fontSize: 12
                },
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                emphasis: {
                    label: {
                        fontSize: 14
                    }
                },
                data: [
                    { value: funnelData.value[1], name: '客户' },
                    { value: funnelData.value[0], name: '线索' },
                    { value: funnelData.value[2], name: '成交客户' }
                ]
            }
        ]
    }
})
// 漏斗图数据
const funnelData = ref([0, 0, 0])

// 漏斗图数据
const getFunnelData = () => {
    crmService.stattisticsGetFunnelData().then(res => {
        // console.log('res4',res)
        funnelData.value[0] = res.data.filter(item => item.label === 'lead')[0].count
        funnelData.value[1] = res.data.filter(item => item.label === 'custom')[0].count
        funnelData.value[2] = res.data.filter(item => item.label === 'dealCustom')[0].count
        // console.log('funnelData',funnelData.value)
    })
}

const store = useStore()
const isLogin = computed(() => {
    const { isLogin } = store.state.auth
    return isLogin
})

onMounted(() => {
    console.log('isLogin', isLogin.value)
    if (isLogin.value){
        getFunnelData()
    }
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
}
</style>