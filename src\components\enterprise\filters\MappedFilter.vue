<script lang="ts" setup>
import aicService from '@/service/aicService'
import type {
    IAicConditionData,
    IAicConditionDataOptionItem,
    IAicNormalSearchRules,
    IIndustryOption,
    INormalFilterParams,
} from '@/types/aic'
import type { RootState } from '@/types/store'
import { computed, inject, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import Checkbox from '../ui/Checkbox.vue'
import type { IPushToGlobal } from '@/types/company'
import MappedCascadeSelect from '../region/MappedCascadeSelect.vue'
import { formatIndustryLabel } from '@/utils/enterprise/industry'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const isUnlimited = computed(() => {
    const list = selectedItems.value
    return list.length === 0
})

// ====================== Refs & Reactive State ======================
const config = ref<IAicConditionData | null>(null)
const regionCascadeSelectRef = ref<{
    reset: () => void
}>()
const showSelect = ref(false)
const selectClosed = ref(true)
const selectedItems = ref<IIndustryOption[]>([])

// ====================== Methods ======================
const setUnlimited = () => {
    regionCascadeSelectRef.value?.reset()
}

const pushSelected = (option: IAicConditionDataOptionItem, tabIndex: number, parent: IAicConditionDataOptionItem) => {
    const level = tabIndex + 1

    selectedItems.value = selectedItems.value.filter((v) => v.item.value !== option.value)

    selectedItems.value.push({ tabIndex: level, item: option })

    if (parent.children) {
        if (parent.value === option.value) {
            console.log('全部项被选中')
            parent.children.forEach((item) => {
                selectedItems.value = selectedItems.value.filter((v) => v.item.value !== item.value)
            })
            selectedItems.value.push({ tabIndex: level - 1, item: option })
        } else {
            let count = 1
            const length = parent.children.length

            const parentOption = JSON.parse(
                JSON.stringify(parent.children.filter((e) => e.label !== '全部'))
            ) as IAicConditionDataOptionItem[]

            parentOption.forEach((item) => {
                const targetIndex = selectedItems.value.findIndex((v) => v.item.value === item.value)
                if (targetIndex !== -1) count += 1
            })

            if (count === length) {
                console.log('全部子项被选中')
                parent.children.forEach((item) => {
                    selectedItems.value = selectedItems.value.filter((v) => v.item.value !== item.value)
                })

                selectedItems.value.push({ tabIndex: level - 1, item: parent })
            }
        }
    }

    console.log('222', JSON.stringify(selectedItems.value))
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}
const openSelect = () => {
    showSelect.value = true
}

const closeSelect = () => {
    showSelect.value = false
}

const pushParams = (options: IIndustryOption[]) => {
    const tempArray: INormalFilterParams[] = []
    options.forEach((option) => {
        const params: INormalFilterParams = {
            label: option.item.label,
            value: formatIndustryLabel(option),
            category: props.data.name,
            categoryKey: props.data.key,
            type: props.data.dataType,
        }
        tempArray.push(params)
    })

    if (!pushToGlobal) return

    if (tempArray.length === 0) {
        pushToGlobal({ categoryKey: props.data.key })
    } else {
        pushToGlobal(tempArray)
    }
}

const onSelectClosed = () => {
    selectClosed.value = true
}

const removeOption = (option: IIndustryOption) => {
    selectedItems.value = selectedItems.value.filter((e) => e.item.value !== option.item.value)
    pushParams(selectedItems.value)
}

const removeAll = () => {
    selectedItems.value = []
    pushParams(selectedItems.value)
}

const getConfig = (item: IAicNormalSearchRules) => {
    if (!config.value) return []
    const value = config.value
    const found = Object.entries(value).find(([k]) => k === item.key)
    const result = found ? (found[1] as IAicConditionData[keyof IAicConditionData]) : []
    return result
}

// ====================== Parent Methods ======================

// ====================== Watchers ======================
watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            setUnlimited()
        }
    }
)

watch(
    () => showSelect.value,
    (value) => {
        if (!value) {
            pushParams(selectedItems.value)
        } else {
            selectClosed.value = false
        }
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getStaticConfig()
})
</script>

<template>
    <div class="region-filter">
        <div class="flex flex-column flex-1 gap-12">
            <div class="font-16 color-black font-weight-500">{{ props.data.name }}</div>
            <div class="flex flex-row flex-wrap gap-12">
                <Checkbox label="不限" value="unlimited" :checked="isUnlimited" :onChange="removeAll" />
                <template v-for="option in selectedItems" :key="option.item.value">
                    <Checkbox
                        :label="option.item.label"
                        :value="option.item.value"
                        :checked="true"
                        :onChange="() => removeOption(option)"
                        :clearable="true"
                    />
                </template>
                <Checkbox label="更多" value="more" :checked="false" :onChange="openSelect" icon="arrow" />
            </div>
        </div>
        <van-popup v-model:show="showSelect" position="bottom" @closed="onSelectClosed">
            <MappedCascadeSelect
                ref="regionCascadeSelectRef"
                :options="getConfig(data)"
                :store-params="storeParams"
                :data="data"
                :close="closeSelect"
                :pushSelected="pushSelected"
                v-if="!selectClosed"
            />
        </van-popup>
    </div>
</template>

<style lang="scss" scoped>
.region-filter :deep(.van-cell__right-icon) {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
