<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import crmService from '@/service/crmService'
import { showNotify } from 'vant'
import type { FormInstance } from 'vant'

const props = defineProps<{
    from: string
}>()
const dialogVisible = ref(false)
type FormType = {
    leadId?: string
    companyName: string
    socialCreditCode: string
    phone?: string
    channel?: string
    note?: string
    clueType?: number
    source?: number
}
const form = reactive<FormType>({
    leadId: '',
    companyName: '',
    socialCreditCode: '',
    phone: '',
    channel: '',
    note: '',
    clueType: props.from === 'lead' ? 2 : 3,
    source: 14, // 线索来源: 新增
})


const formRef = ref<FormInstance>()
const sendLoading = ref(false)
const emit = defineEmits(['refreshData'])
const onSubmit = () => {
    console.log('formRef', formRef, formRef.value)
    if(!formRef.value) return
    formRef.value.validate().then(() => {
        sendLoading.value = true
        crmService.crmAdd(form).then(() => {
            showNotify({ type: 'success', message: '新增成功' })
            dialogVisible.value = false
            emit('refreshData')
        }).finally(() => {
            sendLoading.value = false
        })
    }).catch()
}

watch(() => dialogVisible.value, (newVal) => {
    if (!newVal) {
        form.leadId = ''
        form.companyName = ''
        form.socialCreditCode = ''
        form.phone = ''
        form.channel = ''
        form.note = ''
        form.clueType = props.from === 'lead' ? 2 : 3
        form.source = 14 // 线索来源: 新增
    }
})
</script>
<template>
    <div
        class="!color-blue font-14 tb-pading-4 lr-padding-8 border-radius-4"
        style="border: 1px solid var(--main-blue-); background-color: #e6f0ff"
        @click="dialogVisible=true"
    >
        <van-icon name="plus" />
        新增客户
    </div>

    <!-- 弹框区域 -->
    <van-dialog v-model:show="dialogVisible"  show-cancel-button>
        <template #title>
            <div class="font-16 color-black font-weight-400">新增客户</div>
        </template>
        <van-form label-align="top" ref="formRef">
            <van-cell-group inset>
                <van-field
                    v-model="form.companyName"
                    required
                    name="companyName"
                    label="企业名称"
                    placeholder="请输入企业名称"
                    :rules="[{ required: true, message: '请输入企业名称' }]"
                    clearable
                />
                <van-field
                    v-model="form.socialCreditCode"
                    required
                    name="socialCreditCode"
                    label="企业税号"
                    placeholder="请输入企业税号"
                    :rules="[{ required: true, message: '请输入企业税号' }]"
                    clearable
                />
                <van-field
                    v-model="form.phone"
                    name="phone"
                    label="手机号"
                    placeholder="请输入手机号"
                    :rules="[{pattern:/^1[3-9]\d{9}$/,message:'请输入正确的手机号', validateEmpty:false}]"
                    clearable
                />
                <van-field
                    v-model="form.channel"
                    name="channel"
                    label="客户渠道"
                    placeholder="请输入客户渠道"
                    clearable
                />
                <van-field
                    v-model="form.note"
                    name="note"
                    label="备注"
                    placeholder="请输入备注"
                    clearable
                />
                <div class="font-12 color-two-grey lr-padding-16">提示：新增时将扣除一次线索权益(每家企业仅在首次新增时扣减一次线索权益，后续重复新增不再扣减)。</div>
            </van-cell-group>
        </van-form>
        <template #footer>
            <div class="display-flex b-padding-24 t-padding-16 h-36" style="justify-content: space-evenly;">
                <van-button class="w-120" plain type="primary" style="color: var(--main-blue-);border: 1px solid var(--main-blue-);" @click="dialogVisible = false">取消</van-button>
                <van-button class="w-120" type="primary" style="background-color: var(--main-blue-);" @click="onSubmit" :loading="sendLoading" loading-type="spinner" loading-text="提交中">确认新增</van-button>
            </div>
        </template>
    </van-dialog>
</template>
<style scoped lang="scss">

</style>
