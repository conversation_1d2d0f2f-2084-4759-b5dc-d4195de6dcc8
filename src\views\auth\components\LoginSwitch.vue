<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 通过路由 name 判断
const isPasswordLogin = computed(() => route.name === 'passwordLogin')
const isPhoneLogin = computed(() => route.name === 'phoneLogin')
const isPhoneBind = computed(() => route.name === 'phoneBind')

const toPassword = () => {
    router.replace({
        name: 'passwordLogin',
        query: route.query,
    })
}

const toPhone = () => {
    if (isPhoneBind.value) return
    router.replace({
        name: 'phoneLogin',
        query: route.query,
    })
}
</script>

<template>
    <div class="login-view-switch flex flex-row border-radius-">
        <div
            class="flex lh-22 font-16 flex-1 left-right-center top-bottom-center tb-padding-12"
            :class="{
                'active-btn': isPhoneLogin,
                'left-radius': isPhoneLogin,
            }"
            @click="toPhone"
        >
            {{ isPhoneBind ? '' : '验证码登录' }}
        </div>
        <div
            class="flex lh-22 font-16 flex-1 left-right-center top-bottom-center tb-padding-12"
            :class="{
                'active-btn': isPasswordLogin,
                'right-radius': isPasswordLogin,
            }"
            @click="toPassword"
            v-if="isPasswordLogin || isPhoneLogin"
        >
            账号登录
        </div>
        <div
            class="flex lh-22 font-16 flex-1 left-right-center top-bottom-center tb-padding-12"
            :class="{
                'active-btn': isPhoneBind,
                'right-radius': isPhoneBind,
            }"
            v-if="isPhoneBind"
        >
            手机号绑定
        </div>
    </div>
</template>

<style lang="scss" scoped>
.login-view-switch {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    background: rgba(230, 238, 245, 1);
    overflow: hidden;
}

.active-btn {
    background-color: #ffffff;
}

.right-radius {
    border-top-right-radius: 8px;
}

.left-radius {
    border-top-left-radius: 8px;
}
</style>
