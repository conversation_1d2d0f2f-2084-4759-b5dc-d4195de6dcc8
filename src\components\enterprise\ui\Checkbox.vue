<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps<{
    label: string
    value: string
    onChange: (value: string) => void
    checked: boolean
    icon?: string
    clearable?: boolean
}>()

const isChecked = ref(props.checked)
const onCheck = () => {
    if (!['unlimited', 'more'].includes(props.value)) {
        isChecked.value = !isChecked.value
    }

    props.onChange(props.value)
}

watch(
    () => props.checked,
    (value) => {
        isChecked.value = value
    }
)
</script>

<template>
    <div
        class="font-14 tb-padding-4 lr-padding-10 pointer border-radius-4 flex top-bottom-center left-right-center relative"
        :class="{
            'color-blue': isChecked,
            'back-color-three-blue': isChecked,
            'back-tag-bg': !isChecked,
        }"
        @click="onCheck"
    >
        {{ props.label }}
        <van-icon name="arrow" v-if="icon" />
        <div class="clear-btn" v-if="clearable">
            <van-icon name="clear" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.clear-btn {
    position: absolute;
    right: -6px;
    top: -6px;
    color: var(--main-red-);
}
</style>
