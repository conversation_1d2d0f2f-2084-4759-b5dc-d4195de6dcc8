<template>
    <div class="display-flex space-between" >
        <div v-for="item in dataList" :key="item.id">
            <div class="flex-center flex-column gap-8">
                <div class="font-20 font-weight-600">{{ item.num }}</div>
                <div class="font-14">{{ item.label }}</div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed } from 'vue'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'

const newCrmData = ref([0,0])
const overTimeData = ref([0,0])
const dataList = computed(() => [
    {
        id:1,
        num: newCrmData.value[0],
        label:'新增线索'
    },
    {
        id:2,
        num: newCrmData.value[1],
        label:'新增客户'
    },
    {
        id:3,
        num: overTimeData.value[0],
        label:'超7天未跟进'
    },
    {
        id:4,
        num: overTimeData.value[1],
        label:'超30天未跟进'
    },
])

// 新增线索/客户统计
const getNewCrm = async (num: number) => {
    const res = await crmService.statisticsNewCrmData({days:num})
    newCrmData.value[0] = res.data.filter(item => item.label === '30天新增线索')[0].count
    newCrmData.value[1] = res.data.filter(item => item.label === '30天新增客户')[0].count
}

// 超过XX天线索统计
const getOverTimeCrm = async (num: number) => {
    const res = await crmService.statisticsOvertimeData({days:num})
    if(num === 7){
        overTimeData.value[0] = res.data[0].count
    }else{
        overTimeData.value[1] = res.data[0].count
    }
}

const store = useStore()
const isLogin = computed(() => {
    const { isLogin } = store.state.auth
    return isLogin
})

onMounted(() => {
    if(isLogin.value){
        getNewCrm(30)
        getOverTimeCrm(7)
        getOverTimeCrm(30)
    }

})

</script>

<style lang='scss' scoped>
</style>