<script setup lang="ts">
import indicatorService from '@/service/indicatorService'
import { getCurrentInstance, onMounted, ref } from 'vue'

const instance = getCurrentInstance()
const radarChart = ref<HTMLElement | null>(null)
const radarLoading = ref(true)
const echarts = instance?.appContext.config.globalProperties.$echarts
const props = defineProps<{
    socialCreditCode: string
}>()

const getRadarChartIndicatorData = () => {
    if (!radarChart.value) return
    radarLoading.value = true
    let dataIds = ['1337679975285760', '1337680239526912', '1337680591848448', '1337709490603008', '1337709733872640']
    indicatorService
        .getCompareIndicatorData({ ids: '74,75,76,77,78', socialCreditCode: props.socialCreditCode })
        .then((res) => {
            let data = dataIds.map((id) => {
                return res[id] || 0
            })
            const myChart = echarts.init(radarChart.value)
            myChart.setOption({
                tooltip: {},
                legend: {
                    data: [],
                    padding: 20,
                },

                center: ['50%', '50%'],
                radar: {
                    radius: '60%',
                    // 雷达图的指标
                    indicator: [
                        { name: '经营力', max: 100 },
                        { name: '纳税力', max: 100 },
                        { name: '合规力', max: 100 },
                        { name: '创新力', max: 100 },
                        { name: '发展力', max: 100 },
                    ],
                    splitArea: {
                        show: false, // 隐藏斑马纹
                    },
                    splitNumber: 4,
                },
                series: [
                    {
                        type: 'radar',
                        data: [
                            {
                                value: data,
                                name: '企业评分',
                            },
                        ],
                        areaStyle: {
                            color: 'rgb(218,233,253)',
                        },
                        lineStyle: {
                            color: '#1966ff',
                        },
                        itemStyle: {
                            color: '#1966ff',
                        },
                    },
                ],
            })
        })
        .finally(() => {
            radarLoading.value = false
        })
}
onMounted(() => {
    if (radarChart.value) {
        getRadarChartIndicatorData()
    }
})
</script>
<template>
    <div class="flex center width-100">
        <div ref="radarChart" class="h-300 w-300" v-loading="radarLoading"></div>
    </div>
</template>
<style></style>
