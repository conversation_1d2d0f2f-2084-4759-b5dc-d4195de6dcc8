:root {
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 26px;
    --text-4xl: 30px;
    --text-5xl: 36px;
    --text-6xl: 48px;
    --weight-400: 400;
    --weight-500: 500;
    --weight-600: 600;
    --weight-700: 700;
    --weight-800: 800;
}

//字体相关css，如字体大小，字体颜色，字体样式等
.font-navbar {
    font-size: 14px;
    // font-weight: ;
}

.font-big-title {
    font-size: 40px;
    font-weight: 700;
    line-height: 58px;
}

.font-first-title-active {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
}

.font-first-title-unactive {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

.font-second-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
}

.font-tree-title {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
}

.font-header-label {
    font-size: 14px;
    font-weight: 400;
}
.font-bold {
    font-weight: 700;
}

@for $i from 100 through 1000 {
    @if $i % 100==0 {
        .font-weight-#{$i} {
            font-weight: #{$i};
        }
    }
}

@for $i from 10 through 50 {
    .font-#{$i} {
        font-size: #{$i}px;
    }
}

@for $i from 10 through 50 {
    .lh-#{$i} {
        line-height: #{$i}px;
    }
}

@for $i from 10 through 50 {
    .\!font-#{$i} {
        font-size: #{$i}px !important;
    }
}