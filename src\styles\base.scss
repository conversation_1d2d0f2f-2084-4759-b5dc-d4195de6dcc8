@use 'sass:math';

html,
body {
    margin: 0;
}

p {
    margin: 0;
}

:focus-visible {
    outline: none;
}

:root {
    --main-blue-: #2B83FD;
    --main-three-light-blue-: #E7F2FF;
    --main-green-: #00B273;
    --main-red-: #E95133;
    --main-orange-: #BA7633;
    --gradinent-green: linear-gradient(to right, rgba(0, 178, 115, 0.31), rgba(0, 178, 115, 0.05));
    --gradinent-orange: linear-gradient(to bottom, rgba(233, 81, 51, 0.31), rgba(233, 81, 51, 0.05));
    --gradinent-common-back: linear-gradient(to bottom, #E7F6FE 0%, #F9F9F9 40%,
            #F9F9F9 100%);
    --main-black: #252525;
    --two-grey: #656565;
    --three-grey: #B3B3B3;
    --border-color: #e8e8e8;
    --main-white: #ffffff;
    --four-grey: #878787;
    --second-blue: #f6f8fa;
    --avatar-bg: #80aaff;
    --tag-bg: #f4f4f5;
    --tag-border: #e9e9eb;
    --card-bg: #bfc0c0;
    --three-blue: #e6eeff;
    --five-grey: #f7f8fa;
    --main-bg: #f7f7f7;
    --main-blue-op2-: rgba(25, 102, 255, 0.2);
    --main-background-grey-: #e8e8e8;
    --active-bg-: #e6eeff;
    --table-bg-: #dededf;
    --table-header-bg-: #eef5fe;
    --light-blue-: #99bbff;
    --table-bg-2: #f5f7fa;
    --text-grey: #666666;
    --four-blue: #f5f8ff;
    --two-orange: #ff6a00;
    --menu-bg: #e8efff;
    --order-color: #ba7633;
    --back-grey: #f9f9f9;
    --sz-warning: #ecab28;
}

@mixin hover-bg($css-var) {

    // 基础样式
    & {
        background-color: var(#{$css-var});
        transition: background-color 0.2s; // 添加过渡效果便于观察变化
    }

    // 带有 -xxx 后缀的修饰类
    &--hover {

        // 直接hover生效
        &:hover {
            background-color: var(#{$css-var});
        }

        // 通过父级.hov控制的情况
        .hov &:hover {
            background-color: var(#{$css-var});
        }
    }
}

@mixin hover-color($css-var) {

    // 基础样式
    & {
        color: var(#{$css-var});
    }

    // 带有 -xxx 后缀的修饰类
    &--hover {

        // 直接hover生效 
        &:hover {
            color: var(#{$css-var});
        }

        // 通过父级.hov控制的情况
        .hov &:hover {
            color: var(#{$css-var});
        }
    }
}

@for $i from 1 through 10 {
    .back-color-2b-gradient-blue-#{$i*10} {
        background: linear-gradient(to bottom, rgba(60, 116, 235, math.div($i, 10)), #ffffff)
    }

    .back-color-2r-gradient-blue-#{$i*10} {
        background: linear-gradient(to right, rgba(60, 116, 235, math.div($i, 10)), #ffffff)
    }

    .back-color-2b-gradient-brown-#{$i*10} {
        background: linear-gradient(to bottom, rgba(186, 118, 51, math.div($i, 10)), #ffffff)
    }

    .back-color-2r-gradient-brown-#{$i*10} {
        background: linear-gradient(to right, rgba(186, 118, 51, math.div($i, 10)), #ffffff)
    }

    .color-2b-gradient-blue-#{$i*10} {
        background: linear-gradient(to bottom, rgba(60, 116, 235, math.div($i, 10)), #ffffff);
        color: transparent;
        -webkit-background-clip: text;
    }

    .color-2r-gradient-blue-#{$i*10} {
        background: linear-gradient(to right, rgba(60, 116, 235, math.div($i, 10)), #ffffff);
        color: transparent;
        -webkit-background-clip: text;
    }

    .color-2b-gradient-brown-#{$i*10} {
        background: linear-gradient(to bottom, rgba(186, 118, 51, math.div($i, 10)), #ffffff);
        color: transparent;
        -webkit-background-clip: text;
    }

    .color-2r-gradient-brown-#{$i*10} {
        background: linear-gradient(to right, rgba(186, 118, 51, math.div($i, 10)), #ffffff);
        color: transparent;
        -webkit-background-clip: text;
    }


}

.color-blue {
    @include hover-color(--main-blue-);
}

[class*='!color-blue'] {
    color: var(--main-blue-) !important;
}

.color-primary {
    color: var(--el-color-primary);
}

.color-red {
    color: var(--main-red-);
}

.color-orange {
    color: var(--main-orange-);
}

.color-green {
    color: var(--main-green-);
}

.color-black {
    color: var(--main-black);
}

.color-two-grey {
    color: var(--two-grey);
}

.color-three-grey {
    color: var(--three-grey);
}

.color-border {
    color: var(--border-color);
}

.color-white {
    color: var(--main-white);
}

.back-color-blue {
    @include hover-bg(--main-blue-);
}

.back-color-three-light-blue {
    @include hover-bg(--main-three-light-blue-);
}

.back-color-red {
    background-color: var(--main-red-);
}

.back-color-dark-red {
    background-color: var(--dark-red-);
}

.back-color-orange {
    background-color: var(--main-orange-);
}

.back-color-dark-orange {
    background-color: var(--dark-orange-);
}

.back-color-green {
    background-color: var(--main-green-);
}

.back-color-dark-green {
    background-color: var(--dark-green-);
}

.back-color-gradinent-green {
    background: var(--gradinent-green);
}

.back-color-gradinent-orange {
    background: var(--gradinent-orange);
}

.back-color-gradinent-blue {
    background: var(--gradinent-blue-);
}

.back-color-two-grey {
    background-color: var(--two-grey);
}

.back-color-three-grey {
    background-color: var(--three-grey);
}

.back-color-border {
    background-color: var(--border-color);
}

.back-color-avatar {
    background-color: var(--avatar-bg);
}

.back-tag-bg {
    @include hover-bg(--tag-bg);
}

.back-card-bg {
    @include hover-bg(--card-bg);
}

.back-color-white {
    background-color: var(--main-white);
}

.border-tag {
    border: 1px solid var(--tag-border);
}

.fill-two-grey {
    fill: var(--two-grey) !important;
}

.back-color-three-blue {
    background-color: var(--three-blue);
}

.back-color-common {
    background: var(--gradinent-common-back);
}

.back-color-common-grey {
    background-color: var(--back-grey);
}

.color-five-grey {
    color: var(--five-grey);
}

.back-five-grey {
    background-color: var(--five-grey);
}

.back-color-main {
    @include hover-bg(--main-bg);
}

.back-color-active {
    background-color: var(--active-bg-);
}

.back-color-light-blue {
    @include hover-bg(--light-blue-);
}

.color-text-grey {
    color: var(--text-grey);
}

.back-color-table-bg {
    @include hover-bg(--table-bg-);
}

.back-color-table-bg-2 {
    @include hover-bg(--table-bg-2);
}

.back-color-four-blue {
    @include hover-bg(--four-blue);
}

.color-two-orange {
    color: var(--two-orange);
}

.fill-blue {
    fill: var(--main-blue-) !important;
}

.back-color-menu-bg {
    background-color: var(--menu-bg);
}

.back-transparent {
    background: transparent;
}

.color-order {
    color: var(--order-color);
}

.border-order {
    border: 1px solid var(--order-color)
}

.color-sz-warning {
    color: var(--sz-warning);
}

.back-color-second-blue {
    background-color: var(--second-blue);
}