<template>
    <div class="height-100 back-color-common lr-padding-16 oh display-flex flex-column space-between">
        <div class="background width-100 height-80 relative font-16" id="share-page" :style="{ backgroundImage: bgImg }">
            <div class="relative border-radius-8 width-100 height-100">
                <div 
                    ref="qrcodeContainer" 
                    class="absolute" 
                    :style="
                        { 
                            height: !mpQrSize ? '1.6rem' : mpQrSize === '0' ? '1.6rem' : (parseFloat(mpQrSize) / 16) + 'rem',
                            left: !mpQrLeft ? '70%' : mpQrLeft === '0' ? '70%' : mpQrLeft + '%',
                            top: !mpQrTop ? '76%' : mpQrTop === '0' ? '76%' : mpQrTop + '%',
                        }
                    ">
                    <!-- 用于生成二维码 -->
                    <canvas ref="qrcodeCanvas" style="display:none;"></canvas>
                    <!-- 用于显示&&微信浏览器识别 -->
                    <img ref="qrcodeImg" :src="qrCodeImageSrc" alt="QR Code" style="height:100%">
                </div>
            </div>
        </div>
        <div class="b-margin-12">
            <van-button 
                type="primary" 
                class="h-46 width-100 border-radius-8" 
                style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;" 
                @click=" isWechatBrowser ? saveToWechat() : savePageAsImage()">保存图片
            </van-button>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted } from 'vue'
import html2canvas from 'html2canvas'
import QRCode from 'qrcode'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import BusinessEntyBg from '@/assets/images/business-entry-bg.png'
import fileService from '@/utils/fileService'

const store = useStore<RootState>()
const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})
const mpQrSize = computed(() => {
    const { jinrongEduApplyQrcode } = oemInfo.value || ''
    if(typeof jinrongEduApplyQrcode === 'string' && jinrongEduApplyQrcode){
        const cleanedQrcode = jinrongEduApplyQrcode.replace(/[()]/g, '')
        return cleanedQrcode.split(',')[0]
    }else{
        return null
    }
})

const mpQrTop = computed(() => {
    const { jinrongEduApplyQrcode } = oemInfo.value || ''
    if(typeof jinrongEduApplyQrcode === 'string' && jinrongEduApplyQrcode){
        const cleanedQrcode = jinrongEduApplyQrcode.replace(/[()]/g, '')
        return cleanedQrcode.split(',')[1]
    }else{
        return null
    }
})

const mpQrLeft = computed(() => {
    const { jinrongEduApplyQrcode } = oemInfo.value || ''
    if(typeof jinrongEduApplyQrcode === 'string' && jinrongEduApplyQrcode){
        const cleanedQrcode = jinrongEduApplyQrcode.replace(/[()]/g, '')
        return cleanedQrcode.split(',')[2]
    }else{
        return null
    }
})


const bgImg = computed(() => {
    if(oemInfo?.value?.mpQrCode){
        return `url(${fileService.getFileUrl(oemInfo.value.mpQrCode)})`
    }else{
        return `url(${BusinessEntyBg})`
    }
})

const refCode = ref('')
const qrCodeImageSrc = ref('') // 存储 base64 图片
const qrcodeCanvas = ref<HTMLCanvasElement | null>(null)
const qrcodeContainer = ref<HTMLDivElement | null>(null)

const isWechatBrowser = computed(() => {
    const ua = navigator.userAgent.toLowerCase()
    return /micromessenger/i.test(ua)
})

// 1. 生成图片的函数
const generateImage = async () => {
    const element = document.getElementById('share-page') as HTMLElement
    return await html2canvas(element, {
        useCORS: true, // 是否跨域
        scale: 2, // 清晰度
        logging: false, // 日志
        backgroundColor: '#ffffff' // 背景
    })
}

// 2. 显示图片弹窗让用户长按保存
const showImagePopup = (canvas: HTMLCanvasElement) => {
    const imageUrl = canvas.toDataURL('image/png')
    
    // 创建全屏遮罩层
    const wrapper = document.createElement('div')
    wrapper.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    `
    
    // 创建图片容器
    const img = new Image()
    img.src = imageUrl
    img.style.maxWidth = '90%'
    img.style.maxHeight = '80%'
    
    // 创建提示文字
    const tip = document.createElement('div')
    tip.textContent = '长按图片保存到手机相册'
    tip.style.color = 'white'
    tip.style.marginTop = '20px'
    tip.style.fontSize = '16px'
    
    // 组装元素
    wrapper.appendChild(img)
    wrapper.appendChild(tip)
    wrapper.onclick = () => document.body.removeChild(wrapper)
    
    document.body.appendChild(wrapper)
}

// 3. 主保存函数
const saveToWechat = async () => {
    try {
        const canvas = await generateImage()
        showImagePopup(canvas)
    } catch (error) {
        console.log('error', error)
        // 降级处理：提示用户截图
        alert('保存失败，请手动截图保存')
    }
}


const savePageAsImage = async () => {
    try {
        const canvas = await html2canvas(document.getElementById('share-page') as HTMLElement)
        const image = canvas.toDataURL('image/png')
        // 兼容 Safari 的方式
        const link = document.createElement('a')
        link.href = image
        link.download = 'screenshot.png'
        document.body.appendChild(link) // 必须添加到 DOM 中
        // 如果是 iOS，可能需要提示用户长按图片保存
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            window.setTimeout(() => {
                link.click()
            },300)
        } else {
            link.click()
        }
        document.body.removeChild(link) // 完成后移除
    } catch (error) {
        console.error('保存失败:', error)
    }
}


onMounted(async () => {
    const response = await crmService.crmJRGetRefCode()
    console.log('CrmJRGetRefCode', response.refCode)
    refCode.value = response.refCode
    const qrCodeUrl = `${window.location.origin}/online-measurement?refCode=${refCode.value}`
    // await crmService.crmJRGetRefCode().then(res => {
    //     console.log('CrmJRGetRefCode', res.refCode)
    //     refCode.value = res.refCode
    // })
    if (qrcodeCanvas.value && qrcodeContainer.value) {
        const containerWidth = qrcodeContainer.value.offsetWidth
        const containerHeight = qrcodeContainer.value.offsetHeight
        const qrSize = Math.min(containerWidth, containerHeight)
        console.log('qrCodeUrl', qrCodeUrl)
        // 1. 先在 canvas 上生成二维码
        QRCode.toCanvas(qrcodeCanvas.value, qrCodeUrl, {
            width: qrSize,
            height: qrSize,
            margin: 2, // 增加边距，提高识别率
        }, (error: Error | null) => {
            if (error) {
                console.error('生成二维码失败:', error)
                return
            }
            // 2. 把 canvas 转换成 base64 图片
            qrCodeImageSrc.value = qrcodeCanvas.value!.toDataURL('image/png')
        })
    }
})
</script>

<style lang='scss' scoped>
.background {
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
</style>