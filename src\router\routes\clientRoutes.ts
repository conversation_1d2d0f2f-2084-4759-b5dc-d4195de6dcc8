import SearchHistory from '@/views/company/SearchHistory.vue'
import SearchCompany from '@/views/company/SearchList.vue'
import AttentionList from '@/views/attention/AttentionList.vue'
import ReportList from '@/views/report/ReportList.vue'
import CompanyReport from '@/views/report/CompanyReport.vue'
import ContactList from '@/views/contact/ContactList.vue'
import InvitePage from '@/views/invite/InvitePage.vue'
import InviteFriend from '@/views/invite/InviteFriend.vue'
import ReceiveAward from '@/views/invite/ReceiveAward.vue'
import GetCode from '@/views/get-code/GetCode.vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import Orders from '@/views/orders/Orders.vue'
import OrderDetail from '@/views/orders/orders-detail/OrderDetail.vue'
import GoodsList from '@/views/goods-list/GoodsList.vue'
import MyPoints from '@/views/points/MyPoints.vue'
import PointsDetail from '@/views/points/PointsDetail.vue'
import My from '@/views/my/My.vue'
import Benefit from '@/views/benefit/Benefit.vue'
import BenefitUsageRecord from '@/views/benefit/BenefitUsageRecord.vue'
import { type RouteRecordRaw } from 'vue-router'
import Main from '@/views/Main.vue'
import CustomerHelp from '@/views/customer-help/CustomerHelp.vue'
import ClaimBenefit from '@/views/benefit/ClaimBenefit.vue'
import MyAccount from '@/views/my/MyAccount.vue'
import Authorize from '@/views/authorize/Authorize.vue'
import BusinessEntry from '@/views/business-entry/BusinessEntry.vue'
import OnlineMeasurement from '@/views/business-entry/OnlineMeasurement.vue'
import MyBenefit from '@/views/benefit/MyBenefit.vue'
import craftTable from '@/views/craft-table/CraftTable.vue'
import IndustryMarket from '@/views/industry/IndustryMarket.vue'
import RelativeCompany from '@/views/company/RelativeCompany.vue'
import FollowRecord from '@/views/follow/FollowRecord.vue'
import AddFollowRecord from '@/views/follow/AddFollowRecord.vue'
import LeadPool from '@/views/lead/LeadPool.vue'
import LeadList from '@/views/lead/LeadList.vue'
import CustomerPublicPool from '@/views/lead/CustomerPublicPool.vue'
import CustomerList from '@/views/lead/CustomerList.vue'
import RiskList from '@/views/risk/RiskList.vue'
import RelateCompany from '@/views/company-model-detail/RelateCompany.vue'
import CompanyModelDetail from '@/views/company-model-detail/CompanyModelDetail.vue'
import Search from '@/views/company/search/Search.vue'
import CompanyDetail from '@/views/company/detail/CompanyDetail.vue'
import DetailRelateCompany from '@/views/company/related/RelateCompany.vue'
import LeadDetail from '@/views/leads/lead-detail/LeadDetail.vue'
import MapSearch from '@/views/map-search/MapSearch.vue'
import AdvanceSearch from '@/views/industry/AdvanceSearch.vue'

const clientRoutes: Array<RouteRecordRaw> = [
    {
        path: '/attention',
        name: 'attention',
        component: AttentionList,
        meta: { requiresAuth: true, title: '关注' },
    },
    {
        path: '/report',
        name: 'report',
        component: ReportList,
        meta: { requiresAuth: true, title: '报告' },
    },
    {
        path: '/receive-award',
        name: 'receive-award',
        component: ReceiveAward,
        meta: { requiresAuth: false, title: '新用户专享福利' },
    },
    {
        path: '/online-measurement',
        name: 'online-measurement',
        component: OnlineMeasurement,
        meta: { requiresAuth: false, title: '票易融在线测额' },
    },
    {
        path: '/get-code',
        name: 'getCode',
        component: GetCode,
        meta: { requiresAuth: false, title: '报告' },
    },
    {
        path: '/default',
        name: 'default',
        component: DefaultLayout,
        children: [
            {
                path: '/',
                name: 'home',
                component: Main,
                meta: { requiresAuth: false, title: '首页', keepAlive: true },
            },
        ],
    },
    {
        path: '/navs',
        name: 'user-navs',
        component: AuthLayout,
        children: [
            {
                path: '/my/orders',
                name: 'myOrders',
                component: Orders,
                meta: { requiresAuth: true, title: '我的订单' },
            },
            {
                path: '/my/orders/detail',
                name: 'myOrdersDetail',
                component: OrderDetail,
                meta: { requiresAuth: true, title: '订单详情' },
            },
            {
                path: '/goods/list',
                name: 'goodsList',
                component: GoodsList,
                meta: { requiresAuth: true, title: '购买服务' },
            },
            {
                path: '/customer-help',
                name: 'customerHelp',
                component: CustomerHelp,
                meta: { requiresAuth: false, title: '添加企微' },
            },
            {
                path: '/my/points',
                name: 'points',
                component: MyPoints,
                meta: { requiresAuth: true, title: '我的积分' },
            },
            {
                path: '/my/points-detail',
                name: 'points-detail',
                component: PointsDetail,
                meta: { requiresAuth: true, title: '积分明细' },
            },
            {
                path: '/my/benefit',
                name: 'benefit',
                component: Benefit,
                meta: { requiresAuth: true, title: '权益详情' },
            },

            {
                path: '/my/searchcompany',
                name: 'searchCompany',
                component: SearchCompany,
                meta: { requiresAuth: true, title: '查找企业', keepAlive: true },
            },
            {
                path: '/my/companyreport',
                name: 'companyReport',
                component: CompanyReport,
                meta: { requiresAuth: false, title: '报告' },
            },
            {
                path: '/my/comtactlist',
                name: 'contactList',
                component: ContactList,
                meta: { requiresAuth: false, title: '联系方式' },
            },
            {
                path: '/my/searchhistory',
                name: 'searchhistory',
                component: SearchHistory,
                meta: { requiresAuth: true, title: '查找企业' },
            },
            {
                path: '/my/benefit/usage-record',
                name: 'usage-record',
                component: BenefitUsageRecord,
                meta: { requiresAuth: true, title: '使用明细' },
            },
            {
                path: '/invite-new',
                name: 'invite-new',
                component: InvitePage,
                meta: { requiresAuth: true, title: '邀请新用户' },
            },
            {
                path: '/invite-friend',
                name: 'invite-friend',
                component: InviteFriend,
                meta: { requiresAuth: true, title: '邀请好友' },
            },
            {
                path: '/claim-benefit',
                name: 'claim-benefit',
                component: ClaimBenefit,
                meta: { requiresAuth: false, title: '权益领取' },
            },
            {
                path: '/my/myAccount',
                name: 'myAccount',
                component: MyAccount,
                meta: { requiresAuth: true, title: '我的账号' },
            },
            {
                path: '/my/authorize',
                name: 'authorize',
                component: Authorize,
                meta: { requiresAuth: true, title: '授权协议' },
            },
            {
                path: '/my/business-entry',
                name: 'business-entry',
                component: BusinessEntry,
                meta: { requiresAuth: true, title: '业务进件' },
            },
            {
                path: '/my/my-benefit',
                name: 'my-benefit',
                component: MyBenefit,
                meta: { requiresAuth: true, title: '我的线索权益' },
            },
            {
                path: '/industry-market',
                name: 'industry-market',
                component: IndustryMarket,
                meta: { requiresAuth: true, title: '产业市场' },
            },
            {
                path: '/relative-company',
                name: 'relative-company',
                component: RelativeCompany,
                meta: { requiresAuth: true, title: '关联企业' },
            },
            {
                path: '/follow-record',
                name: 'follow-record',
                component: FollowRecord,
                meta: { requiresAuth: true, title: '跟进记录' },
            },
            {
                path: '/add-follow-record',
                name: 'add-follow-record',
                component: AddFollowRecord,
                meta: { requiresAuth: true, title: '写跟进' },
            },
            {
                path: '/company/search',
                name: 'company-search',
                component: Search,
                meta: { requiresAuth: true, title: '查找企业', keepAlive: true },
            },
            {
                path: '/map-search',
                name: 'mapSearch',
                component: MapSearch,
                meta: { requiresAuth: false },
            },
            {
                path: '/lead-pool',
                name: 'lead-pool',
                component: LeadPool,
                meta: { requiresAuth: true, title: '线索池' },
            },
            {
                path: '/lead-list',
                name: 'lead-list',
                component: LeadList,
                meta: { requiresAuth: true, title: '线索列表' },
            },
            {
                path: '/customer-pool',
                name: 'customer-pool',
                component: CustomerPublicPool,
                meta: { requiresAuth: true, title: '客户公海' },
            },
            {
                path: '/customer-list',
                name: 'customer-list',
                component: CustomerList,
                meta: { requiresAuth: true, title: '客户列表' },
            },
            {
                path: '/risk-list',
                name: 'risk-list',
                component: RiskList,
                meta: { requiresAuth: true, title: '风险列表' },
            },
            {
                path: '/company-model-detail',
                name: 'company-model-detail',
                component: CompanyModelDetail,
                meta: { requiresAuth: false, title: '企业详情' },
            },
            {
                path: '/relate-company',
                name: 'relate-company',
                component: RelateCompany,
                meta: { requiresAuth: false, title: '关联企业' },
            },
            {
                path: '/company/detail',
                name: 'company-detail',
                component: CompanyDetail,
                meta: { requiresAuth: true, title: '企业详情' },
            },
            {
                path: '/company/related',
                name: 'company-related',
                component: DetailRelateCompany,
                meta: { requiresAuth: true, title: '关联企业' },
            },
            {
                path: '/leads/detail',
                name: 'lead-detail',
                component: LeadDetail,
                meta: { requiresAuth: true, title: '线索详情' },
            },
            {
                path: '/customer/detail',
                name: 'customer-detail',
                component: LeadDetail,
                meta: { requiresAuth: true, title: '客户详情' },
            },
            {
                path: '/advance-search',
                name: 'advance-search',
                component: AdvanceSearch,
                meta: { requiresAuth: true, title: '高级搜索' },
            },
        ],
    },
    {
        path: '/invite',
        name: 'invite',
        component: GetCode,
        meta: { requiresAuth: false, title: '' },
    },
    {
        path: '/my',
        name: 'my',
        component: My,
        meta: { requiresAuth: false, title: '我的' },
    },
    {
        path: '/craftTable',
        name: 'craftTable',
        component: craftTable,
        meta: { requiresAuth: false, title: '工作台' },
    },
]

export default clientRoutes
