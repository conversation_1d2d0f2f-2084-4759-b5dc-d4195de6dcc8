<script lang="ts" setup>
import CICON from '@/assets/hub-images/company/company-icon.png'
import aicService from '@/service/aicService'
import type { PersonEnterpriseRelationsItem } from '@/types/aic'
import { closeToast, showFailToast, showLoadingToast } from 'vant'
import { useRouter } from 'vue-router'

const props = defineProps<{
    data: PersonEnterpriseRelationsItem
    name: string
}>()

const router = useRouter()

const toCompany = () => {
    getCompanyInfo()
}

const getCompanyInfo = () => {
    showLoadingToast('即将跳转...')
    aicService
        .searchEnterprise({
            page: 1,
            pageSize: 1,
            keyword: props.data.entName,
            scope: 'companyname',
        })
        .then((res) => {
            const { errCode, data, errMsg } = res
            if (errCode === 0) {
                closeToast()
                if (data.length > 0) {
                    const target = data[0]
                    router.push({
                        name: 'company-detail',
                        query: {
                            socialCreditCode: target.socialCreditCode,
                        },
                    })
                }
            } else {
                showFailToast(errMsg || '未找到相关企业')
            }
        })
        .catch(() => {
            showFailToast('未找到相关企业')
        })
}
</script>

<template>
    <div class="flex flex-column back-color-white all-padding-12 border-radius-8 gap-8" @click="toCompany">
        <div class="flex flex-row gap-8">
            <div class="h-48 w-48">
                <img :src="CICON" alt="" class="height-100 wight-100" />
            </div>
            <div class="flex flex-column gap-4">
                <div class="font-16 color-black">{{ data.entName }}</div>
                <div class="font-16 flex flex-row gap-4 flex-wrap">
                    <template
                        v-for="(value, index) in data.entTags.filter((e) => e.categoryCode !== '007')"
                        :key="value.tagCode"
                    >
                        <div class="small-tag tag-green" v-if="index === 0">
                            {{ value.tagName }}
                        </div>
                        <div class="small-tag tag-blue" v-if="index > 0">
                            {{ value.tagName }}
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="font-14">
            <span class="color-blue">{{ name || '-' }}</span
            >｜{{ data.regCapital || '-' }}｜{{ data.positionStatus || '-' }}
        </div>
    </div>
</template>

<style scoped></style>
