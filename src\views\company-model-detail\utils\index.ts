/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 安全获取嵌套对象属性
 * @param data 源数据对象
 * @param path 属性路径，支持 . 和 [] 语法，如 'a.b[0].c'
 * @param defaultValue 路径不存在时的默认值
 */
export function getNestedValue<T = any>(data: Record<string, any>, path: string, defaultValue?: T): T {
    if (!data || !path) return defaultValue as T

    // 解析路径为访问片段 ['a', 'b', '0', 'c']
    const pathSegments = path.split(/[.[\]]/g).filter((segment) => segment)
    let current: any = data

    for (const segment of pathSegments) {
        if (current === null || current === undefined) break

        // 处理数组索引
        if (Array.isArray(current) && /^\d+$/.test(segment)) {
            const index = Number(segment)
            current = current[index]
        } else {
            // 处理对象属性
            current = current[segment]
        }
    }

    return current !== undefined ? current : (defaultValue as T)
}
export const formatDate = (dateString: string, format = 'YYYY-MM-DD'): string => {
    // 处理不存在或空字符串情况
    if (!dateString || dateString.trim() === '') {
        return '-'
    }

    // 保持原有默认行为：当format为默认值时使用完整日期时间格式
    const actualFormat = format

    // 动态生成目标格式正则表达式
    const targetFormatRegex = new RegExp(
        actualFormat
            .replace(/YYYY/g, 'd{4}')
            .replace(/MM/g, 'd{2}')
            .replace(/DD/g, 'd{2}')
            .replace(/HH/g, 'd{2}')
            .replace(/mm/g, 'd{2}')
            .replace(/ss/g, 'd{2}')
    )

    // 检查是否已为目标格式
    if (targetFormatRegex.test(dateString)) {
        return dateString
    }

    // 尝试解析ISO格式日期
    try {
        const date = new Date(dateString)
        // 验证日期是否有效
        if (isNaN(date.getTime())) {
            return dateString
        }

        // 提取日期组件
        const year = date.getFullYear().toString()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')

        // 根据实际格式进行替换
        return actualFormat
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds)
    } catch (e) {
        // 解析失败时直接返回原始值
        console.error('日期格式化错误:', e)
        return dateString
    }
}
