<script lang="ts" setup>
import type { IAicConditionDataOptionItem, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import { addAllOption, cleanChildren } from '@/utils/flatten'
import type { CascaderOption } from 'vant'
import { onMounted, ref, watch } from 'vue'

interface Option {
    value: string
    label: string
    children?: Option[]
}

const fieldNames = {
    text: 'label',
    value: 'value',
    children: 'children',
}

const props = defineProps<{
    options: IAicConditionDataOptionItem[]
    close: () => void
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
    pushSelected: (v: IAicConditionDataOptionItem, i: number, p: IAicConditionDataOptionItem) => void
}>()

const cascaderValue = ref('')
const cascaderOptions = ref<Option[]>([])
const formatOptions = (options: IAicConditionDataOptionItem[]) => {
    const temp = cleanChildren(options) as IAicConditionDataOptionItem[]
    return addAllOption(temp)
}

const onFinish = ({
    value,
    selectedOptions,
    tabIndex,
}: {
    value: string | number
    selectedOptions: CascaderOption[]
    tabIndex: number
}) => {
    console.log('onFinish')
    console.log(value, selectedOptions, tabIndex)

    const target = selectedOptions[tabIndex] as IAicConditionDataOptionItem
    const prevTarget = selectedOptions[tabIndex - 1] as IAicConditionDataOptionItem

    if (target.label === '全部') {
        props.pushSelected(prevTarget, tabIndex, prevTarget)
    } else {
        // 是否所有的children都被选中

        props.pushSelected(target, tabIndex, prevTarget)
    }

    console.log('关闭')
    props.close()
}

const onClose = () => {
    console.log('close')
    props.close()
}

watch(
    () => props.options,
    (value) => {
        console.log('value', value)
        cascaderOptions.value = formatOptions(value)
    }
)

onMounted(() => {
    cascaderOptions.value = formatOptions(props.options)
})
</script>

<template>
    <van-cascader
        v-model="cascaderValue"
        title="请选择"
        :options="cascaderOptions"
        :field-names="fieldNames"
        @finish="onFinish"
        @close="onClose"
    />
</template>

<style lang="scss" scoped></style>
