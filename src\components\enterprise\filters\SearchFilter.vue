<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, provide, ref, watch } from 'vue'
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { MappedFilter, RegionFilter } from '@/components/enterprise/filters'
import SelectFilter from '@/components/enterprise/filters/SelectFilter.vue'
import MultiSelectFilter from '@/components/enterprise/filters/MultiSelectFilter.vue'

const props = defineProps<{
    show: boolean
    close: () => void
    confirm: () => void
    searchType: string
}>()

const storeInitAction = 'enterprise/initNormalParams'
const storeSetAction = 'enterprise/setNormalParams'
const store = useStore<RootState>()
const rules = ref<IAicNormalSearchRules[]>([])
const config = ref<IAicConditionData | null>(null)
const compressRules = ref<IAicNormalSearchRules[]>([])
const collapse = ref(true)
const showFilterRef = ref(false)
const trigger = ref(0)

const cachedRules = computed(() => {
    const { normalSearchRulesData } = store.state.app || {}
    return JSON.parse(JSON.stringify(normalSearchRulesData)) || []
})

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const getRules = async () => {
    if (cachedRules.value && Array.isArray(cachedRules.value) && cachedRules.value.length > 0) {
        rules.value = formatRules(cachedRules.value)
        compressRules.value = rules.value.slice(6)
        getStaticConfig()
    } else {
        const rulesRes = await aicService.conditionGetInfoForNomal({ searchType: props.searchType })
        store.dispatch('app/setNormalSearchRulesData', rulesRes)

        rules.value = formatRules(JSON.parse(JSON.stringify(rulesRes)))
        compressRules.value = rules.value.slice(6)

        getStaticConfig()
    }
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
    }
}

const formatRules = (rules: IAicNormalSearchRules[]) => {
    // return rules.filter((e) => e.dataType === 'select' || e.dataType === 'multiSelect')
    return rules
}

const isAreaFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'area'
}

const isMappedFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'mapped'
}

const isSelectFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'select'
}

const isMultiSelectFilterShown = (item: IAicNormalSearchRules) => {
    return item.dataType === 'multiSelect'
}

const isCollapse = (index: number) => {
    return index >= 2 && collapse.value
}

// 推送到vuex
const pushToGlobal = (params: INormalFilterParams[]) => {
    console.log('push to global normal filter params', params)
    store.dispatch(storeSetAction, params)
}

const closeFilter = () => {
    showFilterRef.value = false
}

const reset = () => {
    trigger.value = trigger.value + 1
}

watch(
    () => props.show,
    (value) => {
        console.log(value)
        showFilterRef.value = value
    }
)

watch(
    () => showFilterRef.value,
    (value) => {
        if (!value) props.close()
    }
)

// 子组件使用
provide('pushToGlobal', pushToGlobal)

onMounted(() => {
    getRules()
})

onBeforeMount(() => {
    store.dispatch(storeInitAction)
})

provide('reset', trigger)
</script>

<template>
    <van-popup v-model:show="showFilterRef" position="bottom" class="height-80 t-border-radius-12"
               v-bind:closeOnClickOverlay="true">
        <div class="flex flex-column relative height-100 border-box">
            <div class="flex flex-row justify-end all-padding-12">
                <van-icon name="cross" class="color-text-grey" size="20" @click="closeFilter" />
            </div>
            <div class="flex flex-1 flex-column oa border-box lr-padding-12 b-padding-80">
                <div class="flex flex-column gap-18">
                    <template v-for="(item, index) in rules" :key="item.key">
                        <template v-if="isAreaFilterShown(item)">
                            <RegionFilter :data="item" v-show="!isCollapse(index)"
                                          :storeParams="store.state.enterprise.normalFilterParams" />
                        </template>
                        <template v-if="isMappedFilterShown(item)">
                            <MappedFilter :data="item" v-show="!isCollapse(index)"
                                          :storeParams="store.state.enterprise.normalFilterParams" />
                        </template>
                        <template v-if="isSelectFilterShown(item)">
                            <SelectFilter :data="item" :storeParams="store.state.enterprise.normalFilterParams" />
                        </template>

                        <template v-if="isMultiSelectFilterShown(item)">
                            <MultiSelectFilter :data="item" :storeParams="store.state.enterprise.normalFilterParams" />
                        </template>
                    </template>
                </div>
            </div>
            <div class="submit-action font-14 t-padding-8 b-padding-24 back-color-white">
                <div class="width-40">
                    <van-button class="width-100" @click="reset">重置</van-button>
                </div>
                <div class="width-40">
                    <van-button type="primary" class="width-100" @click="confirm">查找企业</van-button>
                </div>
            </div>
        </div>
    </van-popup>
</template>

<style scoped>
.submit-action {
    position: absolute;
    bottom: 0;
    display: flex;
    flex: 1;
    width: 100%;
    flex-direction: row;
    justify-content: space-around;
}
</style>
