<script setup lang="ts">
import aicService from '@/service/aicService'
import type { PersonEnterpriseRelationsItem } from '@/types/aic'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { RelatedListItem } from './components'

const route = useRoute()
const name = route.query.name?.toString() || ''
const entId = route.query.entId?.toString() || ''
const companyName = route.query.companyName?.toString() || ''
const list = ref<PersonEnterpriseRelationsItem[]>([])
const page = ref(1)

const getData = () => {
    aicService
        .gsGetPersonEnterpriseRelations({
            name: name,
            entId: entId,
            companyName: companyName,
            page: page.value,
        })
        .then((res) => {
            const { errCode, data } = res
            const { total, items } = data
            if (errCode === 0) {
                loading.value = false
                list.value = list.value?.concat(items)
                if (total <= list.value?.length) {
                    finished.value = true
                } else {
                    page.value = page.value + 1
                }
            }
        })
        .catch(() => {
            loading.value = false
        })
}

const loading = ref(false)
const finished = ref(false)

// onMounted(() => {
//     getData()
// })
</script>

<template>
    <div class="back-color-main height-100">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getData"
            :offset="150"
            class="gap-8 flex flex-column"
        >
            <RelatedListItem :name="name" :data="value" v-for="value in list" :key="value.pid" />
        </van-list>
    </div>
</template>

<style scoped></style>
