<template>
    <div class="container">
        <div v-if="isLock === 1" class="font-16 text-center t-margin-100">暂无权限查看~</div>
        <div v-else>
            <div
                v-if="page_config.component === 'List'"
                class="font-16 color-two-grey t-padding-10 b-padding-10"
                style="position: sticky; top: 0; background-color: #f1f3f7; z-index: 9999"
            >
                <span> {{ page_config.title }} </span
                ><span class="color-blue">&nbsp;{{ paginationConfig.total }}&nbsp;</span><span>条</span>
            </div>
            <div v-for="(row, index) in list" class="wrapper flex flex-column row-gap-10 b-margin-10" :key="index">
                <div v-for="item in page_config.columns || []" class="flex font-16 lh-18 gap-10" :key="item.key">
                    <div class="w-112 color-two-grey">{{ item.name }}:</div>
                    <div class="flex-1 flex height-100 flex-column color-black">
                        <div
                            v-if="page_config.key === RequestKeys.Investment && item.key === 'ENTNAME' && row.pid"
                            class="pointer"
                            style="color: #509de5"
                            @click="toCompanyDetail(row)"
                        >
                            {{ row.INV || row.ENTNAME }}
                        </div>
                        <div
                            v-else-if="
                                [ColumnType.RelateCompanyLink, ColumnType.CompanyProfileLink].includes(item.type) &&
                                    getValue(row, item.key)
                            "
                            class="flex gap-5 top-bottom-center"
                        >
                            <RelateLink
                                :data="row"
                                :channel-type="channelType"
                                :name="getValue(row, item.key)"
                                :count="row['personRelatedEntNum'] && Number(row['personRelatedEntNum'])"
                                :model-name="props.modelName"
                            />
                        </div>

                        <IText
                            v-else
                            :content="item.render ? item.render(row) : getValue(row, item.key)"
                            :max-lines="item.textCollapse ? 3 : 999999"
                            :defaultExpanded="item.textCollapse || false"
                        />
                    </div>
                </div>
            </div>
            <div v-if="loading" class="text-center font-16 color-two-grey t-margin-10">加载中...</div>
            <div
                v-if="!loading && paginationConfig.total > list.length"
                @click="loadMore"
                class="text-center font-16 color-two-grey t-margin-10"
            >
                点击加载更多...
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue'
import IText from './IText.vue'
import { PageConfig, ColumnType, RequestKeys } from '../config'
import aicService from '@/service/aicService'
import RelateLink from './RelateLink.vue'
import { getNestedValue } from '../utils'
import router from '@/router'
import { showFailToast } from 'vant'

const props = defineProps<{
    modelName: keyof typeof PageConfig
    socialCreditCode: string
}>()
const CURRENT_PAGE = ref(props.modelName)

const paginationConfig = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const list = reactive<any[]>([])
const channelType = ref<number>(0)

const page_config = computed(() => {
    return PageConfig[CURRENT_PAGE.value]
})

// 是否是加载更多（用于区分是刷新还是加载更多）
const isLoadMore = ref(false)

const loading = ref(false)

const isLock = ref<number>(0)

const getList = async () => {
    loading.value = true
    try {
        const res = await aicService.gsInfo({
            modelName: CURRENT_PAGE.value,
            page: paginationConfig.page,
            pageSize: paginationConfig.pageSize,
            socialCreditCode: props.socialCreditCode,
        })
        loading.value = false
        // 如果是List类型的组件，处理分页数据
        if (res && 'items' in res && Array.isArray(res.items)) {
            isLock.value = res.isLock
            // 更新总数
            paginationConfig.total = res.total
            channelType.value = res.channelType

            // 如果是加载更多，则将新数据追加到现有列表
            if (isLoadMore.value) {
                list.push(...res.items)
            } else {
                // 否则替换整个列表
                list.length = 0
                list.push(...res.items)
            }
        }
    } catch (error) {
        console.error('获取列表数据失败：', error)
        loading.value = false
    }
}

// 刷新列表
// const refreshList = () => {
//     paginationConfig.page = 1
//     isLoadMore.value = false
//     getList()
// }

// 加载更多
const loadMore = () => {
    paginationConfig.page += 1
    isLoadMore.value = true
    getList()
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getValue = (row: any, key: string) => {
    return getNestedValue(row, key)
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const toCompanyDetail = (row: any) => {
    const companyName = row.ENTNAME || row.companyName
    console.log(row, companyName)

    if (!companyName) {
        showFailToast({ message: '数据错误' })
        return
    }

    aicService
        .searchEnterprise({
            keyword: companyName,
            scope: 'companyname',
            pageSize: 1,
            page: 1,
        })
        .then((res) => {
            const company = res.data[0]
            if (company?.companyName === companyName) {
                router.push({
                    name: 'companyDetail',
                    query: { socialCreditCode: company.socialCreditCode },
                })
            } else {
                showFailToast({ message: '未找到该公司' })
            }
        })
}
onMounted(() => {
    getList()
})
</script>

<style lang="scss" scoped>
.container {
    min-height: 100%;
    background-color: #f1f3f7;
    padding: 15px;
    box-sizing: border-box;
    padding-top: 5px;
    overflow: auto;
    position: relative;
}
.wrapper {
    padding: 20px;
    border-radius: 15px;
    background-color: #fff;
    height: auto;
}
</style>
