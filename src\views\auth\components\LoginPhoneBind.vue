<script lang="ts" setup>
import systemService from '@/service/systemService'
import userService from '@/service/userService'
import { phoneValidate } from '@/utils/validate'
import { showFailToast } from 'vant'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const showPaddword = ref(false)
const phone = ref('')
const code = ref('')
const loading = ref(false)
const errMsgInfo = ref('')
const countdown = ref(0) // 记录倒计时秒数
const querying = ref(false) // 控制按钮的 loading 状态
const timer = ref<ReturnType<typeof setInterval> | null>(null)
const router = useRouter()
const store = useStore()

const oemkey = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { oemKey } = tenant || {}
    return oemKey
})
const validate = () => {
    let flag = true
    if (!code.value) {
        errMsgInfo.value = '短信验证码不能为空'
        flag = false
    }

    if (!phoneValidate(phone.value.trim())) {
        errMsgInfo.value = '手机号码格式不正确'
        flag = false
    }

    if (!phone.value) {
        errMsgInfo.value = '手机号码不能为空'
        flag = false
    }

    return flag
}

const submitForm = () => {
    if (loading.value) return
    if (!validate()) return

    loading.value = true
    systemService
        .userAuthMobile({
            mobile: phone.value.trim(),
            code: code.value.trim(),
        })
        .then(() => {
            loading.value = false

            userService.userGetAccountInfo().then((account) => {
                store.dispatch('user/setAccountInfo', { ...account })
                if (!sessionStorage.getItem('c-oemkey') && !sessionStorage.getItem('ym-oemkey')) {
                    systemService.systemOEMDetail({ key: oemkey.value, productType: 1 }).then((res) => {
                        // console.log('oemInfo1',res.data)
                        if (res.success && res.data) {
                            sessionStorage.setItem('oemConfig', JSON.stringify(res.data))
                            store.dispatch('auth/setOemConfig', res.data)
                        }
                    })
                }
            })

            router.push('/')
        })
        .catch((err) => {
            loading.value = false
            console.log(err)
        })
}

const onClickRight = () => {
    showPaddword.value = !showPaddword.value
}

const getCode = async () => {
    if (!phone.value) {
        errMsgInfo.value = '手机号码不能为空'
        return
    }

    if (!phoneValidate(phone.value.trim())) {
        errMsgInfo.value = '手机号码格式不正确'
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击
    querying.value = true
    console.log('请求验证码...')
    systemService
        .userSendAuthMobileCode({
            mobile: phone.value.trim(),
        })
        .then((res) => {
            querying.value = false
            const { errCode, errMsg } = res || {}
            if (errCode === 0) {
                console.log('验证码发送成功')
                // 开始倒计时
                countdown.value = 60

                timer.value = setInterval(() => {
                    countdown.value--
                    if (countdown.value <= 0 && timer.value) {
                        clearInterval(timer.value)
                        timer.value = null
                    }
                }, 1000)
            } else {
                showFailToast(errMsg || '短信验证码获取失败')
            }
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const onBack = () => {
    store.dispatch('auth/logout')
    router.push({ name: 'home', query: { active: '0' } })
}
</script>

<template>
    <div class="login-phone-form flex flex-column lr-padding-16 gap-12 t-padding-12">
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black b-margin-8">手机号码</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="phone"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入手机号码"
                    clearable
                />
            </div>
        </div>
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black">验证码</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="code"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入手机验证码"
                    clearable
                    maxlength="6"
                    @click-right-icon="onClickRight"
                >
                    <template #button>
                        <van-button
                            size="small"
                            type="primary"
                            class="smsBtn"
                            :disabled="countdown > 0"
                            @click="getCode"
                            :loading="querying"
                        >
                            {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}</van-button
                        >
                    </template>
                </van-field>
            </div>
        </div>
        <div class="flex flex-row font-12 lh-16 space-between">
            <div class="color-red">{{ errMsgInfo }}</div>
            <div>未绑定手机，请绑定后使用</div>
        </div>
        <van-button class="submit-btn" :loading="loading" @click="submitForm">确认绑定</van-button>
        <van-button class="back-btn" @click="onBack">返 回</van-button>
    </div>
</template>

<style lang="scss" scoped>
.submit-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
    background: linear-gradient(99.4deg, #3c74eb 2.86%, #95d5f4 101.71%);
    border-radius: 8px;
    margin-top: 80px;
    border: none;
}

.smsBtn {
    font-size: 14px;
    color: var(--main-blue-);
    height: 14px;
    background-color: transparent;
    border: none;
}

.login-phone-form :deep(.van-field__button) {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-phone-form :deep(.van-button):before {
    background: transparent;
}

.back-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--three-grey);
    border-radius: 8px;
    margin-top: 4px;
    border: none;
}

.login-phone-form {
    :deep(.van-cell) {
        padding: 0;
        line-height: unset;
    }
    :deep(.van-cell__value) {
        display: flex;
    }
    :deep(.van-field__body) {
        width: 100%;
    }
}
:deep(.van-icon) {
    font-size: 20px;
}
</style>
