<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import BasicSearchItem from './BasicSearchItem.vue'
import type { ICompanyInfo } from '@/types/company'
import { showConfirmDialog, showFailToast, showLoadingToast, showToast, type ActionSheetAction } from 'vant'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import openService from '@/service/openService'
const props = defineProps<{
    refreshing?: boolean
    loading: boolean
    loadData?: () => void
    refreshData?: () => void
    total?: number
    data?: ICompanyInfo[]
    scope?: string
    finished?: boolean
    disRefresh?: boolean
}>()

const currentCompany = ref<ICompanyInfo | null>(null)
const error = ref(false)
const showActionSheet = ref(false)
const actions = [{ name: '转线索' }, { name: '转客户' }]
const list = ref<ICompanyInfo[] | null>(null)
const loadingRef = ref(false)
const refreshingRef = ref(false)
const store = useStore<RootState>()
const { account } = store.state.user || {}
const { user } = account || {}

const actionDescription = computed(() => {
    const { companyName } = currentCompany.value || {}
    return `${companyName}`
})

const showEmpty = computed(() => {
    return list.value !== null && list.value.length === 0 && props.finished
})

const showTotal = computed(() => {
    return list.value !== null && list.value.length > 0
})

const openActionSheet = (data: ICompanyInfo) => {
    currentCompany.value = data
    showActionSheet.value = true
}

const onActionSheetSelect = (_: ActionSheetAction | undefined, index: number, callback?: () => void) => {
    let clueType = 2
    let title = '转线索'
    if (index === 2) {
        clueType = 3
        title = '转客户'
    }

    // 已购买，不提醒扣费
    if (currentCompany.value?.isBuy) {
        return doTransfer(clueType, callback)
    }

    showConfirmDialog({
        title: title,
        message: `${title}将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            doTransfer(clueType, callback)
        })
        .catch(() => {})
}

const doTransfer = (clueType: number, callback?: () => void) => {
    if (!currentCompany.value) return
    showLoadingToast('正在处理...')
    crmService
        .crmAdd({
            clueType: clueType,
            socialCreditCode: currentCompany.value.socialCreditCode,
            companyName: currentCompany.value.companyName,
            source: 1,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                showToast('操作成功')
                updateItemStatus()
                if (callback) callback()
            } else if (errCode === 500 && errMsg.includes('已存在')) {
                showFailToast(errMsg)
                updateItemStatus()
                if (callback) callback()
            } else {
                showToast(errMsg || '操作失败')
            }
        })
        .catch(() => {
            showToast('操作失败')
        })
}

const toJyhy = (data: ICompanyInfo) => {
    currentCompany.value = data
    let clueType = 2
    let title = '企业体检'
    const { isBuy } = data

    if (isBuy) {
        jumpToJyhy(data.socialCreditCode, data.name)
        return
    }
    showConfirmDialog({
        title: title,
        message: `${title}将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            doTransfer(clueType, () => jumpToJyhy(data.socialCreditCode, data.name))
        })
        .catch(() => {})
}

const jumpToJyhy = (socialCreditCode: string, name: string) => {
    if (user?.id && user?.tenantId) {
        const path = `/my/companydetail?socialCreditCode=${socialCreditCode}&companyName=${name}`
        openService
            .ssoAuthentication({
                params: {
                    to: path,
                },
                redirectUrl: import.meta.env.VITE_APP_JYHY_URL + '/stoken',
                tenantId: user.tenantId,
                userId: user.id,
            })
            .then((res) => {
                const { redirectUrl } = res
                if (redirectUrl) {
                    // 单页跳转
                    window.location.href = redirectUrl
                }
            })
    }
}

const updateItemStatus = () => {
    if (!list.value) return
    const targetIndex = list.value.findIndex((e) => e.id === currentCompany.value?.id)
    if (targetIndex === -1) return
    list.value[targetIndex].isBuy = true
}

watch(
    () => props.data,
    (value) => {
        list.value = value || []
    },
    { deep: true }
)

watch(
    () => props.loading,
    (value) => {
        loadingRef.value = value
    }
)

watch(
    () => props.refreshing,
    (value) => {
        refreshingRef.value = value
    }
)
</script>

<template>
    <van-pull-refresh :disabled="disRefresh" v-model="refreshingRef" @refresh="refreshData" class="min-height-100">
        <van-list
            v-model:loading="loadingRef"
            v-model:error="error"
            :finished="finished"
            finished-text="没有更多了"
            @load="loadData"
            error-text="请求失败，点击重新加载"
            :loading-text="refreshingRef ? ' ' : '查询中...'"
            v-if="!showEmpty"
        >
            <div class="flex flex-column all-padding-12 gap-8">
                <div class="font-14 font-weight-500 color-text-grey" v-if="showTotal">
                    找到 <span class="color-blue">{{ total }}</span> 个结果
                </div>
                <BasicSearchItem
                    v-for="item in list"
                    :key="item.id"
                    :data="item"
                    :openActionSheet="openActionSheet"
                    :scope="scope || '0'"
                    :toJyhy="toJyhy"
                />
            </div>
        </van-list>
        <van-empty
            v-if="showEmpty"
            image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
            image-size="80"
            description="未查询到相关数据"
        />
    </van-pull-refresh>

    <van-action-sheet
        v-model:show="showActionSheet"
        :actions="actions"
        cancel-text="取消"
        :description="actionDescription"
        close-on-click-action
        @select="onActionSheetSelect"
    />
</template>

<style scoped></style>
