<script lang="ts" setup>
import { ref, onMounted, watch, onBeforeMount } from 'vue'
import Icon from '@/components/common/Icon.vue'
import Home from './home/<USER>'
import CraftTable from './craft-table/CraftTable.vue'
import My from './my/My.vue'
import { useRoute } from 'vue-router'

defineOptions({
    name: 'MainPage',
})
const route = useRoute()
const active = ref(Number(localStorage.getItem('main-active-tab') || 0))

// 保存当前选中的tab
watch(active, (newVal) => {
    localStorage.setItem('main-active-tab', String(newVal))
})

onBeforeMount(() => {
    if (route.query.active) {
        active.value = Number(route.query.active)
    }
})

onMounted(() => {
    initTabs()
})

const tabs = ref<{ id: number, label: string, icon: string }[]>([])
const initTabs = () => {
    const defaultTabs = [
        {
            id: 0,
            label: '臻企查',
            icon: 'icon-a-zhenqicha',
        },
        {
            id: 1,
            label: '工作台',
            icon: 'icon-a-gongzuotai',
        },
        {
            id: 2,
            label: '我的',
            icon: 'icon-a-wode',
        },
    ] as { id: number, label: string, icon: string }[]
    tabs.value = defaultTabs
}
</script>

<template>
    <div class="main">
        <div class="container">
            <Home v-if="active === 0" />
            <CraftTable v-if="active === 1" />
            <My v-if="active === 2" />
        </div>
        <van-tabbar v-model="active" :safe-area-inset-bottom="true" id="main-tabbar">
            <van-tabbar-item v-for="(item) in tabs" :key="item.id" @click="active = item.id">
                <span :class="{
                    active: active === item.id,
                }">
                    {{ item.label }}
                </span>
                <template #icon="props">
                    <Icon style="cursor: pointer" :icon="item.icon"
                          :color="`${props.active ? 'var(--main-blue-)' : 'var(--table-bg-)'}`" :size="20" />
                </template>
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<style lang="scss" scoped>
.main {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.container {
    flex: 1;
    overflow: auto;
}

:deep(.van-tabbar__placeholder) {
    background-color: transparent !important;
}

.active {
    color: var(--main-blue-);
    font-weight: 500;
}

.un-active {
    color: var(--table-bg-);
}

.label {
    color: var(--table-bg-);
}
</style>
