<template>
    <div v-if="socialCreditCode && modelName" class="height-100" style="overflow-y: auto">
        <component :is="RenderComponent" :socialCreditCode="socialCreditCode" :modelName="modelName" />
    </div>
    <div v-else>参数不能为空</div>
</template>
<script setup lang="ts">
import List from './components/List.vue'
import { useRoute } from 'vue-router'
import Info from './components/Info.vue'
import { computed, onMounted, provide, ref } from 'vue'
import { PageConfig } from './config'
import aicService from '@/service/aicService'
import type { ICompanyInfo } from '@/types/company'
const route = useRoute()
const socialCreditCode = route.query.socialCreditCode as string
const modelName = route.query.modelName as keyof typeof PageConfig
// const modelNameCN = route.query.modelNameCN as string
const id = route.query.id as string
const companyInfo = ref({} as ICompanyInfo)

const getCompanyDetail = () => {
    aicService
        .searchEnterprise({
            keyword: socialCreditCode,
            scope: 'unicode',
        })
        .then((res) => {
            if (res && res.data && res.data.length > 0) {
                companyInfo.value = res.data[0]
            }
        })
}
provide('companyInfo', companyInfo)
const RenderComponent = computed(() => {
    const compoennt = PageConfig[modelName]?.component
    if (compoennt === 'Info') {
        return Info
    }
    if (compoennt === 'List') {
        return List
    }
    return null
})
onMounted(() => {
    if (!id) {
        getCompanyDetail()
    } else {
        companyInfo.value.id = id
    }
})
</script>
