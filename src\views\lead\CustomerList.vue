<script lang="ts" setup>
import { ref, onMounted, onBeforeMount } from 'vue'
import Icon from '@/components/common/Icon.vue'
import searchFilter from '@/views/lead/components/SearchFilter.vue'
import crmService from '@/service/crmService'
import type { IGetCrmLeadParams, ILeadData, IGetTabPool } from '@/types/lead'
import { parseTime } from '@/utils/parse-time'
import aicService from '@/service/aicService'
import systemService from '@/service/systemService'
import type { IAicConditionDataOptionItem } from '@/types/aic'
import customService from '@/service/customService'
import AddLead from '@/views/lead/components/AddLead.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const poolVisible = ref(false)
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const customSearchOptionConifg = ref<CustomConfig>({})

type areaOptionArr = {
    label: string
    value: string
    children?: areaOptionArr[] | []
}
const dealDIGui = (arr: IAicConditionDataOptionItem[]): areaOptionArr[] => {
    return arr.map((item) => ({
        label: item ? item.label : '',
        value: item ? item.value : '',
        children: item.children && item.children.length > 0 ? dealDIGui(item.children) : [],
    }))
}
const getSearchOptions = async () => {
    const [GetDataRes, tagRes, userRes, createUserRes] = await Promise.all([
        aicService.conditionGetData({}),
        crmService.crmTagList({ page: 1, pageSize: 100 }),
        systemService.userGetUserByScopeData('clue'),
        systemService.userGetUserByScopeData('collect'),
    ])
    const transformAreaList = dealDIGui(GetDataRes.area)
    const transformTagList = tagRes.data.map((item) => ({ value: item.id, label: item.tagName }))
    const transformUserList = userRes.data.map((item) => ({ value: item.id, label: item.nickname }))
    const transformCreateUserList = createUserRes.data.map((item) => ({ value: item.id, label: item.nickname }))
    customSearchOptionConifg.value = {
        tagIds: transformTagList,
        areaCode: transformAreaList,
        user: transformUserList,
        beforeUser: transformUserList,
        createUser: transformCreateUserList,
    }
}

const poolList = ref<IGetTabPool[]>([
    {
        name: '全部客户',
        id: 'all',
        sort: 1,
        user: [],
    },
    {
        name: '我的客户',
        id: 'mine',
        sort: 2,
        user: [],
    },
    {
        name: '我协作的客户',
        id: 'with',
        sort: 3,
        user: [],
    },
])
const selectedPoolInfo = ref<IGetTabPool>(
    {
        name: '我的客户',
        id: 'mine',
        sort: 2,
        user: [],
    },
)

const listLoading = ref(false)
const listFinished = ref(false)
let onLoadTimer: number | null = null
const queryParams = ref<IGetCrmLeadParams>({
    page: 0,
    pageSize: 10,
})
const listData = ref<ILeadData[]>([])
const totalNum = ref(0)
const onLoad = async () => {
    if (onLoadTimer) {
        clearTimeout(onLoadTimer)
    }
    onLoadTimer = window.setTimeout(async () => {
        queryParams.value.page += 1
        if (selectedPoolInfo.value.id === 'mine') {
            queryParams.value.searchTag = 2 // 查我的客户
        } else if (selectedPoolInfo.value.id === 'with') {
            queryParams.value.searchTag = 3 // 查我协作的客户
        } else {
            if ('searchTag' in queryParams.value) {
                delete queryParams.value.searchTag
            }
        }

        let res = await customService.customList(queryParams.value)
        if (res.errCode === 0) {
            listLoading.value = false
            const { data, total } = res
            totalNum.value = total
            if (data && data.length > 0) {
                listData.value = listData.value.concat(data)
            }

            if (listData.value.length >= total) {
                listFinished.value = true
            }
        }
    }, 500)
}

onBeforeMount(() => {
    getSearchOptions()
})
onMounted(async () => {
})

const handleChangeActiveTab = (item: IGetTabPool) => {
    selectedPoolInfo.value = item
    queryParams.value.page = 0
    queryParams.value.pageSize = 10
    listData.value = []
    listLoading.value = true
    onLoad()
}
const statusOptions = [
    { label: '未处理', value: 1 },
    { label: '联系方式有效', value: 2 },
    { label: '联系方式无效', value: 3 },
    { label: '关闭', value: 4 },
]
const dealStatus = (val: number) => {
    return (
        statusOptions.find((i) => {
            return i.value === val
        })?.label || '-'
    )
}
const updateSearchParams = (params: IGetCrmLeadParams) => {
    queryParams.value = params
    queryParams.value.page = 0
    queryParams.value.pageSize = 10
    totalNum.value = 0
    listData.value = []
    listLoading.value = true
    onLoad()
}
const callPhone = (val: number | string) => {
    if (!val) return
    // 转换为字符串并移除可能的分隔符
    const phoneNumber = String(val).replace(/[-\s()]/g, '')
    // 使用tel:协议拨打电话
    window.location.href = `tel:${phoneNumber}`
}

const riskLevel2Info = (label?: number) => {
    // ["primary", "success", "info", "warning", "danger"]
    if (label === 100) {
        return {
            type: 'danger',
            label: '高风险',
        }
    } else if (label === 50) {
        return {
            type: 'warning',
            label: '中风险',
        }
    } else if (label === 1) {
        return {
            type: 'success',
            label: '低风险',
        }
    } else if (label === 0) {
        return {
            type: 'primary',
            label: '无风险',
        }
    } else {
        return {
            type: 'primary',
            label: '无风险',
        }
    }
}
const operationStatusLabel2Class = (val: string) => {
    if (val.includes('存续')) {
        return 'green'
    } else if (
        val.includes('迁入') ||
        val.includes('迁出') ||
        val.includes('注销') ||
        val.includes('吊销') ||
        val.includes('撤销')
    ) {
        return 'danger'
    } else {
        return 'primary'
    }
}
const openCustomerDetail = (id: string) => {
    if(!id) return
    router.push({
        name: 'customer-detail',
        query: {
            id: id,
        },
    })
}
</script>
<template>
    <div class="width-100 height-100 relative" style="background-color: #f3f5f8">
    <div class="width-100 height-100 display-flex flex-column absolute top-0 overflow-y-auto border-box">
        <!-- 顶部搜索栏 -->
        <div
            class="display-flex space-between top-bottom-center font-14 all-padding-16"
            style="background-color: #fff"
        >
            <div class="flex-1 color-black display-flex top-bottom-center">
                <div @click="poolVisible = !poolVisible">{{ selectedPoolInfo?.name }}</div>
                <Icon icon="icon-chevron-down" :size="16" color="var(--main-black)" />
                <van-popup v-model:show="poolVisible" position="bottom" round close-on-click-overlay>
                    <div
                        class="h-40 display-flex space-between top-bottom-center tb-padding-8 lr-padding-16 color-black"
                        :class="item.id === selectedPoolInfo?.id ? 'active' : ''"
                        style="border-bottom: 1px solid var(--border-color)"
                        v-for="item in poolList"
                        :key="item.id"
                        @click="handleChangeActiveTab(item)"
                    >
                        <div class="font-16">{{ item.name }}</div>
                        <Icon
                            v-if="item.id === selectedPoolInfo?.id"
                            icon="icon-a-Frame1171275825"
                            :size="28"
                            color="var(--main-blue-)"
                        ></Icon>
                    </div>
                </van-popup>
            </div>
            <searchFilter
                :searchOptionKey="'CUSTOMER_SEARCH_OPTIONS'"
                :customConfig="customSearchOptionConifg"
                :tabType="selectedPoolInfo.id"
                @updateSearchParams="updateSearchParams"
            ></searchFilter>
        </div>
        <!-- 表格区域 -->
        <div class="flex-1 lr-padding-10">
            <div class="display-flex space-between top-bottom-center tb-padding-12">
                <div class="font-13 lr-padding-11" style="color: #8591a6">
                    共{{ totalNum }}条
                </div>
                <div>
                    <AddLead :from="'customer'" :refreshData="updateSearchParams"></AddLead>
                </div>
            </div>
            
            <van-list v-model:loading="listLoading" :finished="listFinished" finished-text="没有更多了" @load="onLoad">
                <div
                    class="border-radius-8 b-margin-8 all-padding-16 color-two-grey"
                    style="background-color: #fff"
                    v-for="(item, index) in listData"
                    :key="index"
                    @click="openCustomerDetail(item.id)"
                >
                    <div class="width-100 display-flex space-between b-margin-12">
                        <img
                            class="w-45 h-45 r-margin-8"
                            src="@/assets/hub-images/company-icon.png"
                            alt="暂无图片"
                        />
                        <div class="flex-1">
                            <div class="display-flex space-between">
                                <div class="maxw-200 color-black font-14 text-ellipsis text-nowrap b-margin-2">
                                    {{ item.companyName || '-' }}
                                </div>
                                <div
                                    v-if="item.contactInfo.mobile"
                                    class="!color-blue font-13"
                                    @click.stop="callPhone(item.contactInfo.mobile)"
                                >
                                    立即拨打
                                </div>
                            </div>
                           
                            <div class="display-flex">
                                <div
                                    v-if="item.operationStatusLabel"
                                    class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12 r-margin-4"
                                    :class="operationStatusLabel2Class(item.operationStatusLabel)"
                                >
                                    {{ item.operationStatusLabel }}
                                </div>
                                <div
                                    class="tb-padding-2 lr-padding-9 lh-16 border-radius-2 font-12"
                                    :class="riskLevel2Info(item.riskLevel).type"
                                >
                                    {{ riskLevel2Info(item.riskLevel).label }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="color-two-grey font-12 b-margin-4">
                        负责人：<span class="color-black">{{ item.user || '-' }}</span>
                    </div>
                    <div class="color-two-grey font-12 b-margin-4">
                        手机号：<span class="color-black">{{ item.contactInfo.mobile || '-' }}</span>
                    </div>
                    <div class="color-two-grey font-12 b-margin-4">
                        跟进状态：<span class="color-black">{{ dealStatus(item.status) }}</span>
                    </div>
                    <div class="color-black font-12 display-flex space-between">
                        <div>{{ item.newFollowDescription || '未跟进' }}</div>
                        <div>
                            上次跟进时间：{{ item.newFollowDate ? parseTime(item.newFollowDate, '{y}-{m}-{d}') : '-' }}
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
    </div>
</template>
<style scoped lang="scss">
.active {
    color: var(--main-blue-);
}
.primary {
    color: var(--main-blue-);
    background-color: #e6f0ff;
}
.green {
    color: var(--main-green-);
    background-color: #e5f8f0;
}
.warning {
    color: var(--main-orange);
    background-color: #fff1e0;
}
.danger {
    color: #fff;
    background-color: #ed5d28;
}
</style>
