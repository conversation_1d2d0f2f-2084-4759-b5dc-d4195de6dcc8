<script setup lang="ts">
import CICON from '@/assets/hub-images/company/company-icon.png'
import aicService from '@/service/aicService'
import crmService from '@/service/crmService'
import type { ICompanyInfo, ICompanyTag, IKeyPersonItem, IShareholderItem } from '@/types/company'
import type { IClueInfo } from '@/types/lead'
import { closeToast, showConfirmDialog, showFailToast, showLoadingToast, showToast } from 'vant'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ContactsView, ModelSections } from './'
import DigitalTaxStatus from './DigitalTaxStatus.vue'
import commonData from '@/js/common-data'
import Icon from '@/components/common/Icon.vue'

const router = useRouter()
const route = useRoute()
const socialCreditCode = route.query.socialCreditCode
const companyDetail = ref<ICompanyInfo | null>(null)
const companyInfo = ref('')
const clueInfo = ref<IClueInfo | null>(null)
const active = ref(0)
const sections = ref<(HTMLElement | null)[]>([])
const expandInfoRef = ref(false)
const shareholderRef = ref<IShareholderItem[]>([])
const keyPersonRef = ref<IKeyPersonItem[]>([])

const getDetail = () => {
    aicService
        .searchEnterprise({
            keyword: socialCreditCode?.toString() || '',
            scope: 'unicode',
            type: 1,
        })
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                companyDetail.value = data[0]
            }
        })
}

const getGsInfo = () => {
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode?.toString() || '',
            modelName: 'EntInfo',
        })
        .then((res) => {
            if (res.companyInfo) {
                companyInfo.value = res.companyInfo
            }
        })
}

const getClueInfo = () => {
    crmService
        .gsGetCompanyClueInfo({
            socialCreditCode: socialCreditCode?.toString() || '',
        })
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                clueInfo.value = data
            }
        })
}
const getShareholder = () => {
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode?.toString() || '',
            modelName: 'Shareholder',
            page: 1,
            pageSize: 99,
        })
        .then((res) => {
            shareholderRef.value = res.items as IShareholderItem[]
        })
}

const getKeyPerson = () => {
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode?.toString() || '',
            modelName: 'KeyPerson',
            page: 1,
            pageSize: 99,
        })
        .then((res) => {
            keyPersonRef.value = res.items as IKeyPersonItem[]
        })
}

const formatTags = (tgas: ICompanyTag[]) => {
    return tgas.filter((e) => e.categoryCode !== '007' && e.categoryCode !== '001')
}

const expandInfo = () => {
    expandInfoRef.value = !expandInfoRef.value
}

const formatInsto = (v: string) => {
    if (isNaN(Number(v))) return v
    return Number(v).toFixed(4) + '%'
}

const handleShareholderClick = (item: IShareholderItem) => {
    const { INVEST_TYPE, INV } = item
    if (INVEST_TYPE === '106-2') {
        router.push({
            name: 'company-related',
            query: {
                name: INV,
                entId: companyDetail.value?.id || '',
            },
        })
    }

    if (INVEST_TYPE === '106-1') {
        toCompanyInfo(INV)
    }
}

const toCompanyInfo = (companyName: string) => {
    showLoadingToast('即将跳转...')
    aicService
        .searchEnterprise({
            page: 1,
            pageSize: 1,
            keyword: companyName,
            scope: 'companyname',
            type: 1,
        })
        .then((res) => {
            const { errCode, data, errMsg } = res
            if (errCode === 0) {
                closeToast()
                if (data.length > 0) {
                    const target = data[0]
                    router.push({
                        name: 'company-detail',
                        query: {
                            socialCreditCode: target.socialCreditCode,
                        },
                    })
                }
            } else {
                showFailToast(errMsg || '未找到相关企业')
            }
        })
        .catch(() => {
            showFailToast('未找到相关企业')
        })
}

const handleKeyPersonClick = (item: IKeyPersonItem) => {
    router.push({
        name: 'company-related',
        query: {
            name: item.name,
            entId: companyDetail.value?.id || '',
        },
    })
}

const provinceName = computed(() => {
    if (companyDetail.value?.province) {
        return commonData.provinces.find((item) => item.tagValue === companyDetail.value?.province)?.name
    } else {
        return ''
    }
})

const onBottomBtnAction = (index: number) => {
    let clueType = 2
    let title = '转线索'
    if (index === 2) {
        clueType = 3
        title = '转客户'
    }

    // 已购买，不提醒扣费
    if (companyDetail.value?.isBuy) {
        return doTransfer(clueType)
    }

    showConfirmDialog({
        title: title,
        message: `${title}将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            doTransfer(clueType)
        })
        .catch(() => {})
}

const doTransfer = (clueType: number) => {
    if (!companyDetail.value) return
    showLoadingToast('正在处理...')
    crmService
        .crmAdd({
            clueType: clueType,
            socialCreditCode: companyDetail.value.socialCreditCode,
            companyName: companyDetail.value.companyName,
            source: 1,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                showToast('操作成功')
                updateBuyStatus()
            } else if (errCode === 500 && errMsg.includes('已存在')) {
                showFailToast(errMsg)
                updateBuyStatus()
            } else {
                showToast(errMsg || '操作失败')
            }
        })
        .catch(() => {
            showToast('操作失败')
        })
}

const copyData = (url: string) => {
    try {
        navigator.clipboard
            .writeText(url)
            .then(function () {
                showToast('复制成功')
            })
            .catch(function (err) {
                showToast(`无法复制文本:${err}`)
                console.error()
            })
    } catch (error) {
        console.log(error)
        showToast(`无法复制文本`)
    }
}

const isCantransfer = computed(() => {
    return companyDetail.value && companyDetail.value.clueInfo.clueType === 0
})

const updateBuyStatus = () => {
    console.log('updateBuyStatusupdateBuyStatusupdateBuyStatus')
    if (companyDetail.value) {
        companyDetail.value.isBuy = true
    }
}

watch(
    () => active.value,
    (value) => {
        if (sections.value[value]) {
            sections.value[value].scrollIntoView({
                behavior: 'smooth', // 平滑滚动
                block: value >= 2 ? 'start' : 'center', // 对齐到顶部
            })
        }
    }
)

onMounted(() => {
    getDetail()
    getGsInfo()
    getClueInfo()
    getShareholder()
    getKeyPerson()
})
</script>
<template>
    <van-skeleton title :row="3" v-if="!companyDetail" />
    <div class="flex flex-column gap-12 back-color-main company-detail oa" v-if="companyDetail">
        <div class="flex flex-column back-color-white all-padding-12 gap-12">
            <div class="flex flex-row gap-8">
                <div class="h-48 w-48">
                    <img :src="CICON" alt="" class="height-100 wight-100" />
                </div>
                <div class="flex flex-column gap-4">
                    <div class="font-18 color-black font-weight-500">
                        {{ companyDetail?.name }}
                    </div>
                    <div class="font-16 flex flex-row gap-8 oh top-bottom-center">
                        <div class="small-tag tag-green">{{ companyDetail.entstatus.slice(0, 2) }}</div>
                        <div
                            class="color-text-grey flex flex-gow top-bottom-center gap-4"
                            @click="copyData(companyDetail.socialCreditCode)"
                        >
                            {{ companyDetail.socialCreditCode }}

                            <Icon icon="icon-copy" size="14" color="grey" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-row flex-wrap gap-4">
                <div class="small-tag tag-blue" v-for="tag in formatTags(companyDetail.companyTags)" :key="tag.tagName">
                    {{ tag.tagName }}
                </div>
            </div>
            <div class="flex flex-row" v-if="companyInfo">
                <div :class="`font-14 text-ellipsis flex-1 ${!expandInfoRef && 'text-nowrap'}`">
                    <span class="color-text-grey">简介：</span>
                    {{ companyInfo }}
                    <span class="font-14 color-blue" @click="expandInfo" v-if="expandInfoRef">收起</span>
                </div>
                <div class="font-14 color-blue" @click="expandInfo" v-if="!expandInfoRef">展开</div>
            </div>

            <div class="flex flex-row font-14 space-around all-padding-12 back-color-second-blue border-radius-4">
                <div class="flex flex-column left-right-center top-bottom-center">
                    <div class="color-text-grey">法定代表人</div>
                    <div class="color-blue">{{ companyDetail.legalperson || '-' }}</div>
                </div>
                <div class="flex flex-column left-right-center top-bottom-center">
                    <div class="color-text-grey">注册资本</div>
                    <div class="color-black">{{ companyDetail.regCapDisplay || '-' }}</div>
                </div>
                <div class="flex flex-column left-right-center top-bottom-center">
                    <div class="color-text-grey">成立日期</div>
                    <div class="color-black">{{ companyDetail.esdate || '-' }}</div>
                </div>
            </div>
            <div class="flex flex-row font-14 space-between">
                <div class="flex flex-row gap-6">
                    <div class="small-round-tag tag-green" v-if="companyDetail.officialWebsite">
                        <van-icon name="certificate" />
                        官网
                    </div>
                    <div class="small-round-tag tag-blue">
                        <van-icon name="location-o" />
                        {{ provinceName }}
                    </div>
                </div>
                <div class="flex flex-row gap-6">
                    <div class="small-round-tag tag-blue tag-border-blue" v-if="clueInfo?.username">
                        负责人：{{ clueInfo?.username || '-' }}
                    </div>
                </div>
            </div>
        </div>
        <DigitalTaxStatus :socialCreditCode="companyDetail.socialCreditCode" />
        <ContactsView :data="companyDetail" :updateBuyStatus="updateBuyStatus" :isBuy="companyDetail.isBuy" />
        <div class="flex flex-column back-color-white all-padding-12 gap-12">
            <div class="flex flex-column flex-1 gap-8">
                <div class="flex flex-row">
                    <div
                        class="w-18 mw-18 font-12 flex tb-padding-10 lr-padding-1 text-center tag-blue r-margin-4 center"
                    >
                        股东{{ shareholderRef.length }}
                    </div>
                    <div class="flex flex-row gap-6 overflow-x-auto hide-scrollbar">
                        <div
                            v-for="(value, index) in shareholderRef"
                            :key="index"
                            class="flex flex-column w-104 mw-104 border-box back-color-second-blue height-100 all-padding-6 border-radius-4 flex flex-column"
                            @click="handleShareholderClick(value)"
                        >
                            <div class="flex flex-1 flex-row gap-4 h-32 oh">
                                <div
                                    class="h-32 w-32 flex top-bottom-center left-right-center font-16 tag-blue b-margin-8 border-radius-4"
                                >
                                    {{ value.INV.slice(0, 1) }}
                                </div>
                                <div
                                    :class="{
                                        'font-12': true,
                                        'color-black': true,
                                        'ellipsis-2': value.INV.length > 4,
                                        'h-36': true,
                                        'flex-1': true,
                                        flex: value.INV.length <= 4,
                                        'top-bottom-center': value.INV.length <= 4,
                                    }"
                                >
                                    {{ value.INV }}
                                </div>
                            </div>
                            <div class="font-10 color-text-grey">
                                持股比例 <span>{{ formatInsto(value.INSTO) }}</span>
                            </div>
                            <div class="font-10 h-10 color-text-grey">
                                <div v-if="value.personRelatedEntNum !== '0'">
                                    关联企业 <span class="color-red">{{ value.personRelatedEntNum }}家</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-row">
                    <div
                        class="w-18 mw-18 font-12 flex tb-padding-10 lr-padding-1 text-center tag-blue r-margin-4 center"
                    >
                        人员{{ keyPersonRef.length }}
                    </div>
                    <div class="flex flex-row gap-6 overflow-x-auto hide-scrollbar">
                        <div
                            v-for="(value, index) in keyPersonRef"
                            :key="index"
                            class="flex flex-column w-104 mw-104 border-box back-color-second-blue height-100 all-padding-6 border-radius-4 flex flex-column"
                            @click="handleKeyPersonClick(value)"
                        >
                            <div class="flex flex-1 flex-row gap-4 h-32 oh">
                                <div
                                    class="h-32 w-32 flex top-bottom-center left-right-center font-16 tag-blue b-margin-8 border-radius-4"
                                >
                                    {{ value.name.slice(0, 1) }}
                                </div>
                                <div class="flex">
                                    <div
                                        :class="{
                                            'font-12': true,
                                            'color-black': true,
                                            'ellipsis-2': value.name.length > 4,
                                            'h-36': true,
                                            flex: value.name.length <= 4,
                                            'top-bottom-center': value.name.length <= 4,
                                        }"
                                    >
                                        {{ value.name }}
                                    </div>
                                </div>
                            </div>
                            <div class="font-10 color-text-grey">
                                <span>{{ value.position_CN }}</span>
                            </div>
                            <div class="font-10 color-text-grey">
                                关联企业 <span class="color-red">{{ value.personRelatedEntNum }}家</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ModelSections
            :socialCreditCode="companyDetail.socialCreditCode"
            :name="companyDetail.companyName"
            :isBuy="companyDetail.isBuy"
            :updateBuyStatus="updateBuyStatus"
        />
        <div class="bottom-btn" v-if="isCantransfer">
            <div
                class="width-50 flex flex-column center font-12 color-text-grey"
                @click="(onBottomBtnAction(1), () => updateBuyStatus())"
            >
                <Icon icon="icon-xiansuo" size="24" color="black" />
                转线索
            </div>
            <div
                class="width-50 flex flex-column center font-12 color-text-grey"
                @click="(onBottomBtnAction(2), () => updateBuyStatus())"
            >
                <Icon icon="icon-kehu" size="24" color="black" />
                转客户
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.company-detail {
    position: relative;
    padding-bottom: 64px;
}

.bottom-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 4;
    background-color: white;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding-top: 6px;
    padding-bottom: 6px;
}
</style>
