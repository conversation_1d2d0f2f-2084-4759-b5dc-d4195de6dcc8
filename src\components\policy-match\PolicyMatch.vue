<template>
    <div class="display-flex space-between top-bottom-center">
        <div
            class="font-14 tb-padding-8 lr-padding-12 color-blue back-color-2b-gradient-blue-10 font-weight-600 border-radius-8"
        >
            依托政策红利，尽享资金扶持
        </div>
        <div class="text-center">
            <div class="font-24 font-bold color-blue">20-100万</div>
            <div class="font-14 color-three-grey">企业平均补贴</div>
        </div>
    </div>
    <div
        v-for="item in matchList"
        :key="item.name"
        class="display-flex back-color-2b-gradient-blue-10 border-radius-8 tb-padding-10 lr-padding-12 t-margin-8 space-between top-bottom-center"
    >
        <div>
            <div class="display-flex top-bottom-center">
                <div class="font-16">{{ item.name }}</div>
                <div
                    class="border font-12 tb-padding-4 lr-padding-12 color-three-grey l-margin-16"
                    style="color: #666666"
                >
                    匹配度1%
                </div>
            </div>
            <div class="display-flex t-margin-10">
                <div class="border font-12 tb-padding-4 lr-padding-12 color-three-grey" style="color: #666666">
                    省级
                </div>
            </div>
        </div>
        <div>
            <div class="btn" style="width: 2.0511rem; height: 0.7786rem" @click="toSeekHelp">领取额度</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import crmService from '@/service/crmService'
import type { IGoodsPolicyItem } from '@/types/lead'

import { useRouter } from 'vue-router'

const router = useRouter()
const toSeekHelp = () => {
    router.push({
        name: 'customerHelp',
    })
}

const socialCreditCode = inject('socialCreditCode') as Ref<string>

const matchList: Ref<IGoodsPolicyItem[]> = ref([])

const getList = () => {
    crmService.crmGoodsPolicyEntMatchRule({ socialCreditCode: socialCreditCode.value }).then((res) => {
        matchList.value = res
            .map((item) => {
                return {
                    ...item,
                    ...item.matchScore,
                    name: item.name,
                }
            })
            .slice(0, 10)
        console.log('获取产品匹配列表结果', matchList.value)
    })
}

onMounted(() => {
    getList()
})
</script>

<style scoped></style>
