<script lang="ts" setup>
import authService from '@/service/authService'
import userService from '@/service/userService'
import systemService from '@/service/systemService'
import type { IAuthLogin } from '@/types/auth'
import { showFailToast } from 'vant'
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

const route = useRoute()
const store = useStore()
const showPaddword = ref(false)
const username = ref('')
const password = ref('')
const logging = ref(false)
const router = useRouter()
const errMsgInfo = ref('')
const inviter = route.query.inviter

const oemkey = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { oemKey } = tenant || {}
    return oemKey
})

const validate = () => {
    let flag = true

    if (!username.value) {
        errMsgInfo.value = '用户名不能为空'
        flag = false
    }

    if (!password.value) {
        errMsgInfo.value = '密码不能为空'
        flag = false
    }

    return flag
}

const submitForm = () => {
    if (logging.value) return
    if (!validate()) return

    const credentials = {
        password: password.value,
        username: username.value.trim(),
        grant_type: 'password',
        inviterCode: '',
    }

    if (inviter && typeof inviter === 'string') {
        credentials.inviterCode = inviter
    }

    logging.value = true

    // login(credentials)

    authService
        .oauthOpenCaptcha({
            username: username.value,
        })
        .then((res) => {
            const { data } = res || {}
            if (data) {
                authService.oauthCaptcha(
                    credentials,
                    (credentials) => {
                        return login(credentials)
                    },
                    () => {
                        logging.value = false
                    }
                )
            } else {
                login(credentials)
            }
        })
        .catch(() => {
            logging.value = false
        })
}

const login = (credentials: IAuthLogin) => {
    authService
        .mobileOauthToken(credentials)
        .then((res) => {
            logging.value = false
            const { errCode, data, errMsg } = res || {}
            if (errCode === 0) {
                const { auth } = data || {}
                store.dispatch('auth/loginSuccess', data)

                if (auth === 1) {
                    userService.userGetAccountInfo().then((account) => {
                        store.dispatch('user/setAccountInfo', { ...account })
                        if (!sessionStorage.getItem('c-oemkey') && !sessionStorage.getItem('ym-oemkey')) {
                            systemService.systemOEMDetail({ key: oemkey.value, productType: 1 }).then((res) => {
                                // console.log('oemInfo1',res.data)
                                if (res.success && res.data) {
                                    sessionStorage.setItem('oemConfig', JSON.stringify(res.data))
                                    store.dispatch('auth/setOemConfig', res.data)
                                }
                            })
                        }
                    })
                    if (sessionStorage.getItem('claimBenefit')) {
                        router.push(sessionStorage.getItem('claimBenefit') as string)
                        sessionStorage.clear()
                    } else {
                        router.push({
                            name: 'home',
                        })
                    }
                } else {
                    router.push({
                        name: 'phoneBind',
                    })
                }
            } else if (errCode === 700) {
                // 弹出验证码
                authService.oauthCaptcha(
                    credentials,
                    (credentials) => {
                        return login(credentials)
                    },
                    () => {
                        logging.value = false
                    }
                )
            } else {
                showFailToast(errMsg || '登录失败')
            }
        })
        .catch((err) => {
            console.log(err)
            logging.value = false
            showFailToast('系统错误，请稍后再试')
        })
}

const onClickRight = () => {
    showPaddword.value = !showPaddword.value
}

const onBack = () => {
    router.push({ name: 'home', query: { active: '0' } })
    // const previousRoute = router.options.history.state.back
    // if (previousRoute) {
    //     router.go(-1)
    // } else {
    //     router.push({ name: 'home' })
    // }
}
</script>

<template>
    <div class="login-password-form flex flex-column lr-padding-16 gap-12 t-padding-12">
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black b-margin-8">账号</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="username"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入登录账号"
                    clearable
                />
            </div>
        </div>
        <div class="flex flex-column gap-8">
            <div class="font-14 lh-20 color-black">密码</div>
            <div class="flex h-44 border border-radius-8 lr-padding-16 top-bottom-center">
                <van-field
                    v-model="password"
                    class="font-14 lh-20 h-20 all-padding-0"
                    placeholder="请输入账号密码"
                    clearable
                    :type="showPaddword ? 'text' : 'password'"
                    :right-icon="!showPaddword ? 'closed-eye' : 'eye-o'"
                    @click-right-icon="onClickRight"
                />
            </div>
        </div>
        <div class="flex flex-row font-12 lh-16">
            <div class="color-red">{{ errMsgInfo }}</div>
            <div></div>
        </div>
        <van-button class="submit-btn" :loading="logging" @click="submitForm">登 录</van-button>
        <van-button class="back-btn" @click="onBack">返 回</van-button>
    </div>
</template>

<style lang="scss" scoped>
.submit-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
    background: linear-gradient(99.4deg, #3c74eb 2.86%, #95d5f4 101.71%);
    border-radius: 8px;
    margin-top: 80px;
    border: none;
}

.back-btn {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--three-grey);
    border-radius: 8px;
    margin-top: 4px;
    border: none;
}

.login-password-form {
    :deep(.van-cell) {
        padding: 0;
        line-height: unset;
    }

    :deep(.van-cell__value) {
        display: flex;
    }

    :deep(.van-field__body) {
        width: 100%;
    }
}

:deep(.van-icon) {
    font-size: 20px;
}
</style>
