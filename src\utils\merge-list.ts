/**
 * 合并新旧列表并去重
 * @param oldList 已有列表
 * @param newList 新请求到的列表
 * @param key 唯一键名（默认 'id'）
 * @returns 去重后的新列表
 */
// eslint-disable-next-line
export const mergeList = <T extends Record<string, any>>(
    oldList: T[],
    newList: T[],
    key: keyof T = 'id' as keyof T
): T[] => {
    const map = new Map<T[keyof T], T>()

    ;[...oldList, ...newList].forEach((item) => {
        map.set(item[key], item) // 新数据覆盖旧数据
    })

    return Array.from(map.values())
}
