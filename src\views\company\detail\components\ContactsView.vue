<script lang="ts" setup>
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import type { ContactItem, GetCompanyContactResponse, ICompanyInfo, IContactSourceItem } from '@/types/company'
import { closeToast, showConfirmDialog, showLoadingToast } from 'vant'
import { onMounted, ref, watch } from 'vue'

const props = defineProps<{
    data: ICompanyInfo | null
    updateBuyStatus: () => void
    isBuy: boolean
}>()

const contacts = ref<GetCompanyContactResponse | null>(null)
const showMoreContacts = ref(false)
const loading = ref(false)
const queried = ref(false)
const moreContactsActive = ref(0)

const buyContactsConfirm = () => {
    showConfirmDialog({
        title: '解锁联系方式',
        message: `解锁联系方式，将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            buyContacts()
        })
        .catch(() => {})
}

const buyContacts = () => {
    if (!props.data) return

    showLoadingToast({
        message: '解锁中...',
        forbidClick: true,
        loadingType: 'spinner',
    })

    orderService
        .orderBuyLegal({
            companyName: props.data.name,
            socialCreditCode: props.data.socialCreditCode,
            serviceKey: 'xs',
        })
        .then((res) => {
            closeToast()
            if (res.contacts && contacts.value) {
                contacts.value.contacts = res.contacts
                contacts.value.isLock = 0
                props.updateBuyStatus()
            }
        })
}

const formatContacts = () => {
    if (!contacts.value || !contacts.value.contactNum) return []
    if (contacts.value.contactNum <= 4) return contacts.value.contacts

    const filterData = contacts.value.contacts.filter((e) => e.tagType === 1 || e.tagType === 2)

    return filterData
}

const formatTagType = (tagType: number) => {
    if (tagType === 1) return '关键'
    if (tagType === 2) return '推荐'
    return ''
}

const formatSource = (sources?: IContactSourceItem[]) => {
    if (!sources || sources.length === 0) return ''
    return sources[0].sourceName
}

const isShowPhoneBtn = (item: ContactItem) => {
    return item.type === 1 || item.type === 2
}

const getContacts = () => {
    loading.value = true
    aicService
        .gsGetContacts({
            socialCreditCode: props.data?.socialCreditCode?.toString() || '',
        })
        .then((res) => {
            loading.value = false
            contacts.value = res
        })
        .finally(() => {
            loading.value = false
            queried.value = true
        })
}

const formatContactsTags = () => {
    if (!contacts.value) return []
    const type1 = contacts.value.contacts.filter((e) => e.type === 1)
    const type2 = contacts.value.contacts.filter((e) => e.type === 2)
    const type3 = contacts.value.contacts.filter((e) => e.type === 3)
    const type4 = contacts.value.contacts.filter((e) => e.type === 4)

    const list = [
        {
            label: `全部(${contacts.value.contacts.length})`,
            value: 0,
        },
        {
            label: `手机(${type1.length})`,
            value: 1,
        },
        {
            label: `固话(${type2.length})`,
            value: 2,
        },
        {
            label: `QQ(${type3.length})`,
            value: 3,
        },
        {
            label: `邮箱(${type4.length})`,
            value: 4,
        },
    ]

    return list
}

const formatContactsList = () => {
    if (!contacts.value) return []
    if (moreContactsActive.value === 0) return contacts.value.contacts
    return contacts.value.contacts.filter((e) => e.type === moreContactsActive.value)
}

watch(
    () => props.isBuy,
    (value) => {
        if (value) {
            getContacts()
        }
    }
)

onMounted(() => {
    getContacts()
})
</script>
<template>
    <div class="flex flex-column back-color-white all-padding-12 gap-12 space-between">
        <div class="flex flex-row space-between color-text-grey">
            <div class="font-14 color-black font-weight-500">
                联系方式<span v-if="contacts && contacts.contactNum">({{ contacts.contactNum }})</span>
            </div>
            <div class="font-12 flex flex-row top-bottom-center" @click="showMoreContacts = true">
                更多
                <van-icon name="arrow" />
            </div>
        </div>
        <van-skeleton title :row="3" v-if="loading" />
        <div v-if="queried && !contacts">暂无联系方式</div>
        <div v-if="contacts?.isLock === 1" class="h-145 back-color-second-blue width-100 flex flex-column center gap-8">
            <Icon icon="icon-a-lock" :size="32" color="main-blue" />
            <div class="font-14 font-weight-500 color-black">使用权益解锁联系方式</div>
            <van-button type="primary" size="small" style="height: 28px; font-size: 12px" @click="buyContactsConfirm">
                立即解锁
            </van-button>
        </div>
        <template v-if="contacts?.isLock !== 1">
            <div
                class="font-14 flex flex-row space-between justify-center"
                v-for="contact in formatContacts()"
                :key="contact.contact"
            >
                <div>
                    <div class="color-black flex flex-row top-bottom-center gap-6">
                        <div
                            :class="{
                                'small-round-tag': true,
                                'tag-blue': contact.tagType === 1,
                                'tag-gold': contact.tagType === 2,
                            }"
                        >
                            <van-icon name="good-job" size="14" v-if="contact.tagType === 2" />
                            <van-icon name="star" size="12" v-if="contact.tagType === 1" />
                            {{ formatTagType(contact.tagType) }}
                        </div>
                        <div>{{ contact.content }}</div>
                        <div class="color-text-grey">{{ contact.numArea || '' }}</div>
                    </div>
                    <div class="color-text-grey">
                        <span v-if="contact.contact">{{ contact.contact }}</span>
                        <span v-if="contact.positionContent">【{{ contact.positionContent }}】</span>
                        <span v-if="contact.sources && (contact.contact || contact.positionContent)"> | </span>
                        <span v-if="contact.sources">来源：{{ formatSource(contact.sources) }}</span>
                    </div>
                </div>
                <div class="color-blue" v-if="isShowPhoneBtn(contact)">
                    <van-icon name="phone-o" :size="12" />
                    <a :href="`tel:${contact.contact}`" class="color-blue font-12">拨打电话</a>
                </div>
            </div>
        </template>
        <van-popup
            v-model:show="showMoreContacts"
            position="bottom"
            class="height-80 t-border-radius-12"
            v-bind:closeOnClickOverlay="true"
        >
            <div class="flex flex-column lr-padding-12 t-padding-18 b-padding-12 height-100 oh border-box">
                <div class="flex flex-row gap-8 b-margin-6">
                    <div
                        :class="{
                            'small-round-tag': true,
                            'tag-blue': moreContactsActive === item.value,
                        }"
                        v-for="item in formatContactsTags()"
                        :key="item.value"
                        @click="
                            () => {
                                moreContactsActive = item.value
                            }
                        "
                    >
                        {{ item.label }}
                    </div>
                </div>
                <div class="flex flex-1 flex-column oa">
                    <div
                        class="font-14 flex flex-row space-between justify-center contact-item"
                        v-for="contact in formatContactsList()"
                        :key="contact.contact"
                    >
                        <div>
                            <div class="color-black flex flex-row top-bottom-center gap-6">
                                <div
                                    :class="{
                                        'small-round-tag': true,
                                        'tag-blue': contact.tagType === 1,
                                        'tag-gold': contact.tagType === 2,
                                    }"
                                    v-if="[1, 2].includes(contact.tagType)"
                                >
                                    <van-icon name="good-job" size="14" v-if="contact.tagType === 2" />
                                    <van-icon name="star" size="12" v-if="contact.tagType === 1" />
                                    {{ formatTagType(contact.tagType) }}
                                </div>
                                <div>{{ contact.content }}</div>
                                <div class="color-text-grey">{{ contact.numArea || '' }}</div>
                            </div>
                            <div class="color-text-grey">
                                <span v-if="contact.contact">{{ contact.contact }}</span>
                                <span v-if="contact.positionContent">【{{ contact.positionContent }}】</span>
                                <span v-if="contact.sources && (contact.contact || contact.positionContent)"> | </span>
                                <span v-if="contact.sources">来源：{{ formatSource(contact.sources) }}</span>
                            </div>
                        </div>
                        <div class="color-blue" v-if="isShowPhoneBtn(contact)">
                            <van-icon name="phone-o" :size="12" />
                            <a :href="`tel:${contact.contact}`" class="color-blue font-12">拨打电话</a>
                        </div>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<style scoped lang="scss">
.contact-item {
    border-bottom: 1px solid var(--border-color);
    padding-top: 8px;
    padding-bottom: 8px;
}
</style>
