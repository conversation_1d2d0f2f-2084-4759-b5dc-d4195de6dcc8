<script lang="ts" setup>
import POSTER01 from '@/assets/hub-images/home/<USER>'
import openService from '@/service/openService'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
const store = useStore<RootState>()
const { account } = store.state.user || {}
const { user } = account || {}

const list = [
    {
        img: POSTER01,
        action: () => {
            jumpToJyhy()
        },
    },
]

const jumpToJyhy = () => {
    console.log('jumpToJyhy')
    if (user?.id && user?.tenantId) {
        openService
            .ssoAuthentication({
                params: {
                    to: '/',
                },
                redirectUrl: import.meta.env.VITE_APP_JYHY_URL + '/stoken',
                tenantId: user.tenantId,
                userId: user.id,
            })
            .then((res) => {
                const { redirectUrl } = res
                if (redirectUrl) {
                    window.open(redirectUrl)
                }
            })
    } else {
        window.open(import.meta.env.VITE_APP_JYHY_URL)
    }
}
</script>

<template>
    <div class="home-swipe">
        <van-swipe class="home-van-swipe" :autoplay="3000" indicator-color="white">
            <van-swipe-item :autoplay="3000" v-for="(item, index) in list" :key="index">
                <img :src="item.img" class="width-100 height-100 img-cover" @click="item.action()" />
            </van-swipe-item>
        </van-swipe>
    </div>
</template>

<style scoped>
.home-swipe {
    height: 141px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 16px;
    padding-right: 16px;
}

.home-van-swipe {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
}
</style>
