<template>
    <div class="filter-box border-radius-6 back-color-white display-flex top-bottom-center left-right-center"
         @click="showFilterPopup">
        <van-icon name="filter-o" size="16" />
    </div>

    <SearchFilter searchType="mapSearch" :show="showFilterPopupFlag" :close="closeFilter" :confirm="onFilterConfirm" />

    <!-- 
    <van-popup v-model:show="showFilterPopupFlag" closeable round position="right" close-icon-position="top-right"
        :style="{ width: '80%', height: '100%' }" close-icon="close">

        <div class="display-flex flex-column height-100 ">
            <div class="flex-grow-1 t-padding-40 lr-padding-12">

              
            </div>
            <div style="height: 50px;" class="display-flex gap-20 lr-padding-12">
                <div class="flex-1">
                    <van-button class="width-100">重置</van-button>
                </div>
                <div class="flex-1">
                    <van-button type="primary" class="width-100">查询</van-button>
                </div>
            </div>

        </div>



    </van-popup> -->
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, defineEmits } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
const store = useStore<RootState>()
import { SearchFilter } from '@/components/enterprise/filters'

const searchParams = computed(() => {
    const { normalFilterParams } = store.state.enterprise

    return normalFilterParams
})

const emits = defineEmits(['filterChange'])
const onFilterConfirm = () => {
    console.log(searchParams.value)
    let obj = {
        contact: '', // 联系方式
        entstatus: '', // 营业状态
        establishment: [],
        registercapital: '', // 注册资金
        enttype: [],
        industry: []
    }
    for (let item of searchParams.value) {
        obj[item.categoryKey] = item.value
    }
    console.log('obbbb', obj)
    emits('filterChange', obj)
    showFilterPopupFlag.value = false
}

const closeFilter = () => {
    showFilterPopupFlag.value = false
}


const showFilterPopupFlag = ref(false)

const showFilterPopup = () => {
    showFilterPopupFlag.value = true
    console.log('showFilterPopup')
}

onMounted(() => {
    store.dispatch('app/setNormalSearchRulesData', null)
})

</script>

<style lang='scss' scoped>
.filter-box {
    width: 40px;
    height: 40px;
}
</style>