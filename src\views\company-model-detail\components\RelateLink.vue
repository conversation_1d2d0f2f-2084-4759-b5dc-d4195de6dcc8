<template>
    <template v-if="channelType === 1">
        <div v-if="hasMultipleRelations" class="pointer link-text" @click="handlerOpenRelate(data)">
            {{ name }}【关联{{ Number(count) }}家企业】
        </div>

        <template v-else-if="modelName === 'Shareholder'">
            <div v-if="data.pid" class="pointer link-text" @click="getCompanyDetail(name)">{{ name }}</div>
            <div v-else-if="!hasEnoughRelations">{{ name }}</div>
        </template>

        <div v-else-if="isNonClickableModel">{{ name }}</div>

        <div v-else-if="isCompanyWithNoRelations" class="pointer link-text" @click="getCompanyDetail(name)">
            {{ name }}
        </div>

        <div v-else>{{ name }}</div>
    </template>

    <template v-else-if="channelType === 2">
        <div v-if="Number(count) > 0" class="pointer link-text" @click="handlerOpenRelate(data)">
            {{ name }}
        </div>

        <div v-else-if="isCompanyWithPid" class="pointer link-text" @click="getCompanyDetail(name)">
            {{ name }}
        </div>

        <div v-else-if="!count" class="pointer link-text" @click="dealPersonOrCompany(data)">
            {{ name }}
        </div>

        <div v-else>{{ name }}</div>
    </template>
</template>

<script lang="ts" setup>
import { computed, inject, type Ref } from 'vue'
import aicService from '@/service/aicService'
import { useRouter } from 'vue-router'
import { showFailToast } from 'vant'
import { PageConfig, RequestKeys } from '../config'
import type { ICompanyInfo } from '@/types/company'
const companyInfo = inject('companyInfo') as Ref<ICompanyInfo>
const router = useRouter()
const props = defineProps<{
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: Record<string, any>

    channelType: number
    name: string
    count: number
    modelName: keyof typeof PageConfig
}>()

// 计算属性优化条件判断
const countNumber = computed(() => Number(props.count))
const hasMultipleRelations = computed(() => countNumber.value > 1)
const hasEnoughRelations = computed(() => countNumber.value >= 2)
const isNonClickableModel = computed(() => [RequestKeys.Investment, RequestKeys.KeyPerson].includes(props.modelName))
const isCompanyWithNoRelations = computed(() => countNumber.value === 0)
const isCompanyWithPid = computed(() => countNumber.value === 0 && props.data.pid)
// 获取公司详情
const getCompanyDetail = (companyName: string = '') => {
    if (!companyName) {
        showFailToast({ message: '数据错误' })
        return
    }
    aicService
        .searchEnterprise({
            keyword: companyName,
            scope: 'companyname',
            pageSize: 1,
            page: 1,
        })
        .then((res) => {
            const company = res.data[0]
            if (company?.companyName === companyName) {
                router.push({
                    name: 'company-detail',
                    query: { socialCreditCode: company.socialCreditCode },
                })
            } else {
                showFailToast({ message: '未找到该公司' })
            }
        })
}

interface Relate {
    frpid?: string
    pid?: string
    INV?: string
    LEGALPERSON?: string
    name?: string
    companyName?: string
    legalperson?: string
}

// 处理个人或公司
const dealPersonOrCompany = (row: Relate) => {
    if (row.frpid) {
        // 存在frpid 指定为小蓝本对外投资法定代表人
        row.pid = row.frpid
        handlerOpenRelate(row)
    } else {
        const pid = row.pid || ''
        const isPersonId = pid.startsWith('p_')

        if (isPersonId) {
            // 查不到企业信息 ==> 人
            handlerOpenRelate(row)
        } else {
            // 查到企业信息
            getCompanyDetail(row.INV)
        }
    }
}

// 打开关联关系弹窗
const handlerOpenRelate = (data: Relate) => {
    console.log('data', data)
    router.push({
        name: 'relate-company',
        query: {
            entId: data.pid || companyInfo.value.id,
            name: data.INV || data.LEGALPERSON || data.name || data.legalperson,
        },
    })
}
</script>

<style lang="scss" scoped>
.link-text {
    color: #509de5;
}

.pointer {
    cursor: pointer;
}
</style>
