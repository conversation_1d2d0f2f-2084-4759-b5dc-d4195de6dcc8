import type { IAicConditionDataOptionItem } from '@/types/aic'

// 支持泛型树形扁平化
export function flatten<T extends { children?: T[] }>(data: T[]): T[] {
    const result: T[] = []
    data.forEach((item) => {
        result.push(item)
        if (item.children) {
            result.push(...flatten(item.children))
        }
    })
    return result
}

type WithChildren<T> = T & { children?: WithChildren<T>[] }
export function cleanChildren<T extends object>(
    data: WithChildren<T> | WithChildren<T>[]
): WithChildren<T> | WithChildren<T>[] {
    if (Array.isArray(data)) {
        return data.map((item) => cleanChildren(item) as WithChildren<T>)
    } else {
        const newItem = { ...data } as WithChildren<T>
        if (newItem.children && newItem.children.length > 0) {
            newItem.children = cleanChildren(newItem.children) as With<PERSON><PERSON>dren<T>[]
            if (newItem.children.length === 0) {
                delete newItem.children
            }
        } else {
            delete newItem.children
        }
        return newItem
    }
}

export const addAllOption = (nodes: IAicConditionDataOptionItem[]): IAicConditionDataOptionItem[] => {
    return nodes.map((node) => {
        const newNode: IAicConditionDataOptionItem = {
            ...node,
        }

        if (node.children && node.children.length > 0) {
            // 递归处理子节点
            const processedChildren = addAllOption(node.children)

            // 在 children 开头加上 “全部”
            newNode.children = [
                {
                    label: '全部',
                    value: node.value,
                    pinyin: '',
                },
                ...processedChildren,
            ]
        }

        return newNode
    })
}
