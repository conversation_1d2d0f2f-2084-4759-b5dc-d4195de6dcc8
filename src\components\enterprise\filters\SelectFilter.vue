<script lang="ts" setup>
import type { IAicNormalSearchRuleEnums, IAicNormalSearchRules, INormalFilterParams } from '@/types/aic'
import type { IPushToGlobal } from '@/types/company'
import { computed, inject, onBeforeMount, ref, watch, type Ref } from 'vue'

// ====================== Interfaces & Types ======================
interface ISelectFilter extends IAicNormalSearchRuleEnums {
    checked?: boolean
}

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const unlimited = ref(true)
const list = ref<ISelectFilter[]>([])

// ====================== Methods ======================
const onChange = (value: string) => {
    // 查找目标项
    const targetItem = list.value.find((item) => item.tagValue === value)

    // 处理未找到目标项的情况
    if (!targetItem) {
        unlimited.value = true
        resetParams()
        return
    }

    unlimited.value = false

    // 处理选中状态切换
    if (targetItem.checked) {
        // 如果已选中，则取消选中
        targetItem.checked = false
    } else {
        // 如果未选中，先取消所有项的选中状态，再选中当前项
        list.value.forEach((item) => (item.checked = false))
        targetItem.checked = true
    }

    pushParams(targetItem)
}

const pushParams = (item: ISelectFilter) => {
    const params: INormalFilterParams = {
        label: item.name,
        value: item.tagValue,
        category: props.data.name,
        categoryKey: props.data.key,
        type: props.data.dataType,
        checked: item.checked,
        paramType: props.data.paramType || '',
    }

    if (!pushToGlobal) return
    pushToGlobal(params)
}

const resetParams = () => {
    if (!pushToGlobal) return
    pushToGlobal({ categoryKey: props.data.key })
}

const isUnlimited = computed(() => {
    const list = props.storeParams.filter((e) => e.categoryKey === props.data.key)
    return list.length === 0
})

// ====================== Watchers ======================
watch(
    () => unlimited.value,
    (value) => {
        if (value) {
            list.value.forEach((item) => {
                item.checked = false
            })
        }
    }
)

watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            list.value.forEach((item) => {
                item.checked = false
            })
        }
    }
)

// ====================== Lifecycle Hooks ======================
onBeforeMount(() => {
    const { enums } = props.data || {}
    list.value = enums || []
})

const reset = inject('reset') as Ref<number>

watch(reset, () => {
    console.log('重置')
    onChange('unlimited')
})
</script>

<template>
    <div class="select-filter">
        <div class="flex flex-column flex-1 gap-12">
            <div class="font-16 color-black font-weight-500">{{ props.data.name }}</div>
            <div class="flex flex-row flex-wrap gap-12">
                <Checkbox label="不限" value="unlimited" :checked="isUnlimited" :onChange="onChange" />
                <Checkbox
                    v-for="item in list"
                    :key="item.tagValue"
                    :label="item.name"
                    :value="item.tagValue"
                    :checked="!!item.checked"
                    :onChange="onChange"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
