<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <div v-if="relativeCompanyList.length === 0" class="t-margin-50 flex-center">
                <img src="@/assets/images/points/no-benefit-bg.png" alt="" style="width: 5rem; height: 5rem;">
            </div>
            <van-cell v-else v-for="item in relativeCompanyList" :key="item.pid">
                <div class="display-flex flex-column" style="text-align: left;">
                    <div class="display-flex gap-8">
                        <img src="@/assets/hub-images/relative-company-touxiang.png" alt="" height="50" >
                        <div class="display-flex flex-column">
                            <span class="font-16 font-weight-600 color-black">{{ item.entName }}</span>
                            <div class="display-flex gap-4">
                                <div v-for="(tag, index) in item.entTags.slice(0, 3)" :key="tag.tagCode">
                                    <div v-if="index === 0 " class="display-flex lr-padding-8 border-radius-4" style="background-color: #E5F8F0;">
                                        <span class="font-12" style="color: #26C96A;">{{ tag.tagName }}</span>
                                    </div>
                                    <div v-if="index === 1" class="display-flex lr-padding-8 border-radius-4" style="background-color: #FFEDD7;">
                                        <span class="font-12" style="color: #ECAB28;">{{ tag.tagName }}</span>
                                    </div>
                                    <div v-if="index === 2" class="display-flex lr-padding-8 border-radius-4" style="background-color: #E6F0FF;">
                                        <span class="font-12" style="color: #2B83FD;">{{ tag.tagName }}</span>
                                    </div>
                                </div>
                                <div v-if="item.entTags.length > 3" class="display-flex lr-padding-8 border-radius-4" style="background-color: #E6F0FF;">
                                    <span class="font-12" style="color: #2B83FD;">···</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="display-flex gap-4 font-12 color-black">
                        <span class="color-blue">{{ name || '-' }}</span>
                        <span>|</span>
                        <span>{{ item.regCapital || '-' }}</span>
                        <span>|</span>
                        <span>{{ item.positionStatus || '-' }}</span>
                    </div>
                    <div class="font-12 color-black">
                        {{ item.position || '-' }}
                    </div>
                </div>
            </van-cell>
        </van-list>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import type { GsGetPersonEnterpriseRelationsParams, PersonEnterpriseRelationsItem } from '@/types/aic'
import aicService from '@/service/aicService'
import { useRoute } from 'vue-router'

const route = useRoute()
const relativeCompanyList = ref<PersonEnterpriseRelationsItem[]>([])
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

const queryParams = ref<GsGetPersonEnterpriseRelationsParams>({
    page: 1,
    entId: '',
    name: '',
    companyName: '',
})

const search = async (params: GsGetPersonEnterpriseRelationsParams) => {
    const res = await aicService.gsGetPersonEnterpriseRelations(params)
    relativeCompanyList.value.push(...res.data.items)
    return res
}

let ti = null
const onLoad = () => {
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        const res = await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        // console.log('relativeCompanyList.value.length',relativeCompanyList.value.length)
        if (relativeCompanyList.value.length === res.data.total) {
            finished.value = true
        }
    },100)
}
const name = ref('')
onMounted(() => {
    // console.log('mounted', route.query)
    name.value = route.query.name as string
    queryParams.value = {
        page: 1,
        entId: route.query.entId as string ,
        name: route.query.name as string ,
        companyName: route.query.companyName as string ,
    }
})
</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

:deep(.van-cell) {
    margin-bottom: 8px;
    border-radius: 8px;
}
</style>