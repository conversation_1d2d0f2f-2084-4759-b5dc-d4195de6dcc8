<script lang="ts" setup>
import { computed } from 'vue'
import aiButton from '@/assets/lottiefiles/ai-button.json'
import soundWave from '@/assets/lottiefiles/sound-wave.json'
import openService from '@/service/openService'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
const router = useRouter()

const store = useStore()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})
const envRedirectUrl = import.meta.env.VITE_APP_OPENAPI_SSO_AUTHENTICATION

const openAiUrl = () => {
    if (userInfo.value?.id) {
        openService
            .ssoAuthentication({
                params: {
                    to: '/a/qyjy',
                    redirect_url: window.location.href,
                    redirect_name: '企业慧眼',
                },
                redirectUrl: envRedirectUrl,
                tenantId: userInfo.value?.tenantId || '',
                userId: userInfo.value?.id || '',
            })
            .then((res) => {
                const { redirectUrl } = res
                if (redirectUrl) {
                    // 单页跳转
                    window.location.href = redirectUrl
                }
            })
    } else {
        router.push('/phone-login')
    }
}
</script>

<template>
    <div class="home-main-img">
        <div class="t-margin-6">
            <LottieAnimation
                :animationData="soundWave"
                :loop="true"
                :autoplay="true"
                :width="'100%'"
                :height="'100%'"
            />
        </div>
        <div class="width-100 t-margin-6" @click="openAiUrl()">
            <LottieAnimation :animationData="aiButton" :loop="true" :autoplay="true" :width="'100%'" :height="'100%'" />
        </div>
    </div>
</template>

<style scoped>
.home-main-img {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
}
</style>
