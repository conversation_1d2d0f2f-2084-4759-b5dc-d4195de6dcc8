<script lang="ts" setup>
import aicService from '@/service/aicService'
import type { IAicConditionData, IAicNormalSearchRules, INormalFilterParams, Region } from '@/types/aic'
import type { RootState } from '@/types/store'
import { computed, inject, onMounted, provide, ref, watch, type Ref } from 'vue'
import { useStore } from 'vuex'
import Checkbox from '../ui/Checkbox.vue'
import RegionCascadeSelect from '../region/RegionCascadeSelect.vue'
import { formatRegionLabel } from '@/utils/enterprise/region'
import type { IPushToGlobal } from '@/types/company'

// ====================== Interfaces & Types ======================
export interface ParentMethods {
    postSelectedRegions: (regions: Region[]) => void
}

interface Option {
    value: string
    label: string
    children?: Option[]
}

// ====================== Props & Injections ======================
const props = defineProps<{
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
}>()

const pushToGlobal = inject<(v: IPushToGlobal) => void>('pushToGlobal', () => {})

const reset = inject('reset') as Ref<number>

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const cachedStaticConfig = computed<IAicConditionData | null>(() => {
    const { staticConfig } = store.state.app || {}
    return JSON.parse(JSON.stringify(staticConfig)) || null
})

const isUnlimited = computed(() => {
    const list = selectedRegions.value
    return list.length === 0
})

// ====================== Refs & Reactive State ======================
const config = ref<IAicConditionData | null>(null)
const regionCascadeSelectRef = ref<{
    reset: () => void
}>()
const showSelect = ref(false)
const selectedRegions = ref<Region[]>([])
const cascaderOptions = ref<Option[]>([])
const selectClosed = ref(true)

// ====================== Methods ======================
const setUnlimited = () => {
    regionCascadeSelectRef.value?.reset()
}

const handlePostRegions = (regions: Region[]) => {
    selectedRegions.value = regions
}

const pushSelectRegion = (region: Region) => {
    const { children, parent } = region

    if (parent) {
        selectedRegions.value = selectedRegions.value.filter((v) => v.value !== parent.value)
    }

    if (children) {
        children.forEach((item) => {
            selectedRegions.value = selectedRegions.value.filter((v) => v.value !== item.value)
        })
    }

    selectedRegions.value = selectedRegions.value.filter((v) => v.value !== region.value)

    selectedRegions.value.push(region)

    if (parent && parent.children) {
        let count = 1
        const length = parent.children.length
        parent.children.forEach((item) => {
            if (item.value === parent.value) return true

            const targetIndex = selectedRegions.value.findIndex((v) => v.value === item.value)
            if (targetIndex !== -1) count += 1
        })

        if (count === length) {
            parent.children.forEach((item) => {
                selectedRegions.value = selectedRegions.value.filter((v) => v.value !== item.value)
            })
            selectedRegions.value.push(parent)
        }
    }
}

const getStaticConfig = async () => {
    if (cachedStaticConfig.value) {
        config.value = cachedStaticConfig.value
        cascaderOptions.value = formatOptions()
    } else {
        const configRes = await aicService.conditionGetData()
        store.dispatch('app/setStaticConfig', configRes)
        config.value = JSON.parse(JSON.stringify(configRes))
        cascaderOptions.value = formatOptions()
    }
}

const formatOptions = () => {
    return convertFirstLevelRegionsToOptions(config.value?.area || [])
}

function getFirstLetter(pinyin: string): string {
    return pinyin.charAt(0).toUpperCase()
}

function sortRegionsByPinyin(regions: Region[]): Region[] {
    return [...regions].sort((a, b) => {
        const letterA = getFirstLetter(a.pinyin)
        const letterB = getFirstLetter(b.pinyin)
        return letterA.localeCompare(letterB)
    })
}

function convertFirstLevelRegionsToOptions(regions: Region[]) {
    const sortedRegions = sortRegionsByPinyin(regions)
    return sortedRegions.map((region) => ({
        value: region.value,
        label: region.label,
        children: region.children,
    }))
}

const openSelect = () => {
    showSelect.value = true
}

const closeSelect = () => {
    showSelect.value = false
}

const pushParams = (regions: Region[]) => {
    const tempArray: INormalFilterParams[] = []
    regions.forEach((region) => {
        const params: INormalFilterParams = {
            label: formatRegionLabel(region),
            value: region.value,
            category: props.data.name,
            categoryKey: props.data.key,
            type: props.data.dataType,
        }
        tempArray.push(params)
    })

    if (!pushToGlobal) return

    if (tempArray.length === 0) {
        pushToGlobal({ categoryKey: props.data.key })
    } else {
        pushToGlobal(tempArray)
    }
}

const onSelectClosed = () => {
    selectClosed.value = true
}

const removeRegion = (item: Region) => {
    selectedRegions.value = selectedRegions.value.filter((region) => region.value !== item.value)
}

const removeAllRegion = () => {
    selectedRegions.value = []
    pushParams(selectedRegions.value)
}

// ====================== Parent Methods ======================
const parentMethods: ParentMethods = {
    postSelectedRegions: (region: Region[]) => handlePostRegions(region),
}

provide<ParentMethods>('parentMethods', parentMethods)

// ====================== Watchers ======================
watch(
    () => isUnlimited.value,
    (value) => {
        if (value) {
            setUnlimited()
        }
    }
)

watch(
    () => showSelect.value,
    (value) => {
        if (!value) {
            pushParams(selectedRegions.value)
        } else {
            selectClosed.value = false
        }
    }
)

watch(reset, () => {
    console.log('重置')
    removeAllRegion()
})

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getStaticConfig()
})
</script>

<template>
    <div class="region-filter">
        <div class="flex flex-column flex-1 gap-12">
            <div class="font-16 color-black font-weight-500">{{ props.data.name }}</div>
            <div class="flex flex-row flex-wrap gap-12">
                <Checkbox label="不限" value="unlimited" :checked="isUnlimited" :onChange="removeAllRegion" />
                <template v-for="item in selectedRegions" :key="item.value">
                    <Checkbox
                        :label="formatRegionLabel(item)"
                        :value="item.value"
                        :checked="true"
                        :onChange="() => removeRegion(item)"
                        :clearable="true"
                    />
                </template>
                <Checkbox label="更多" value="more" :checked="false" :onChange="openSelect" icon="arrow" />
            </div>
        </div>
        <van-popup v-model:show="showSelect" position="bottom" @closed="onSelectClosed">
            <RegionCascadeSelect
                ref="regionCascadeSelectRef"
                :regions="config?.area || []"
                :store-params="storeParams"
                :data="data"
                :close="closeSelect"
                :pushSelectRegion="pushSelectRegion"
                v-if="!selectClosed"
            />
        </van-popup>
    </div>
</template>

<style lang="scss" scoped>
.region-filter :deep(.van-cell__right-icon) {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
