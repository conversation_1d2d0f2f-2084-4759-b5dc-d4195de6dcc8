export default {
    provinces: [
        {
            name: '山西',
            tagValue: '14',
        },
        {
            name: '浙江',
            tagValue: '33',
        },
        {
            name: '台湾',
            tagValue: '71',
        },
        {
            name: '宁夏',
            tagValue: '64',
        },
        {
            name: '新疆',
            tagValue: '65',
        },
        {
            name: '河南',
            tagValue: '41',
        },
        {
            name: '西藏',
            tagValue: '54',
        },
        {
            name: '澳门',
            tagValue: '82',
        },
        {
            name: '贵州',
            tagValue: '52',
        },
        {
            name: '江西',
            tagValue: '36',
        },
        {
            name: '湖南',
            tagValue: '43',
        },
        {
            name: '广西',
            tagValue: '45',
        },
        {
            name: '陕西',
            tagValue: '61',
        },
        {
            name: '香港',
            tagValue: '81',
        },
        {
            name: '吉林',
            tagValue: '22',
        },
        {
            name: '河北',
            tagValue: '13',
        },
        {
            name: '黑龙江',
            tagValue: '23',
        },
        {
            name: '四川',
            tagValue: '51',
        },
        {
            name: '安徽',
            tagValue: '34',
        },
        {
            name: '辽宁',
            tagValue: '21',
        },
        {
            name: '湖北',
            tagValue: '42',
        },
        {
            name: '上海',
            tagValue: '31',
        },
        {
            name: '北京',
            tagValue: '11',
        },
        {
            name: '重庆',
            tagValue: '50',
        },
        {
            name: '福建',
            tagValue: '35',
        },
        {
            name: '甘肃',
            tagValue: '62',
        },
        {
            name: '海南',
            tagValue: '46',
        },
        {
            name: '江苏',
            tagValue: '32',
        },
        {
            name: '内蒙古',
            tagValue: '15',
        },
        {
            name: '青海',
            tagValue: '63',
        },
        {
            name: '山东',
            tagValue: '37',
        },
        {
            name: '天津',
            tagValue: '12',
        },
        {
            name: '云南',
            tagValue: '53',
        },
        {
            name: '广东',
            tagValue: '44',
        },
    ],
    leadsTabs: [
        {
            title: '线索信息',
            value: 'leadsInfo',
        },
        {
            title: '产品匹配',
            value: 'goodsEntMatchRule',
        },
        {
            title: '政策匹配',
            value: 'policyEntMatchRule',
        },
    ],
    lxySearchTagTypes: [
        {
            label: '智能搜索',
            val: '0',
            plac: '智能搜索，输入任意关键词，以空格隔开',
            searchKeys: [
                {
                    key: 'businessscope',
                    title: '经营范围',
                },
                {
                    key: 'history_name_ws',
                    title: '历史名称',
                },
                {
                    key: 'b2bInfo',
                    title: '企业简介',
                },
                {
                    key: 'baike',
                    title: '百科简介',
                },
                {
                    key: 'auction',
                    title: '招标投标',
                },
                {
                    key: 'brandName_ws',
                    title: '品牌信息',
                },
                {
                    key: 'products',
                    title: '产品',
                },
            ],
        },
        {
            label: '找企业',
            val: 'companyname',
            plac: '请输入企业名称',
            searchKey: 'history_name_ws',
            searchTitle: '历史名称',
        },
        {
            label: '找人脉',
            val: 'personnel',
            plac: '请输入一个完整姓名（支持企业法人/高管/其他员工）',
            searchTitle: '相关人员',
            searchKey: 'personnel_info',
        },
        {
            label: '地址',
            val: 'companyaddress',
            plac: '请输入地址，支持搜索通讯地址和注册地址',
            searchKey: 'regAddress',
            searchTitle: '注册地址',
        },
        {
            label: '经营范围',
            val: 'businessscope',
            plac: '请输入主营业务或经营范围关键词',
            searchTitle: '经营范围',
            searchKey: 'businessscope',
        },
        {
            label: '号码',
            val: 'contactWay',
            plac: '请输入联系方式，包括手机号（请输入11位完整手机号）、固话、邮箱',
            searchTitle: '邮箱',
            searchKey: 'email',
        },
    ],
    mapContactListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '有手机',
        },
        {
            value: '2',
            label: '有固话',
        },
        {
            value: '3',
            label: '有邮箱',
        },
        {
            value: '4',
            label: '有QQ',
        },
        {
            value: '5',
            label: '无联系方式',
        },
    ],
    mapStatusListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '在营/存续',
        },
        {
            value: '4',
            label: '迁入/迁出',
        },
        {
            value: '2',
            label: '吊销/撤销',
        },
        {
            value: '3',
            label: '注销',
        },
        {
            value: '8',
            label: '停业',
        },
        {
            value: '9',
            label: '其他',
        },
    ],
    mapCapitalListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '20万以下',
        },
        {
            value: '2',
            label: '20~50万',
        },
        {
            value: '3',
            label: '50~100万',
        },
        {
            value: '4',
            label: '100~200万',
        },
        {
            value: '5',
            label: '200~500万',
        },
        {
            value: '6',
            label: '500万以上',
        },
    ],
    mapEnterpriseTypeListEnum: [
        {
            value: 0,
            label: '全部类型',
        },
        {
            value: 1,
            label: '民营',
        },
        {
            value: 2,
            label: '国有企业',
        },
        {
            value: 3,
            label: '外资、中外合资',
        },
        {
            value: 4,
            label: '港、澳、台投资',
        },
        {
            value: 5,
            label: '工商个体户',
        },
        {
            value: 6,
            label: '其他企业',
        },
        {
            value: 7,
            label: '社会组织',
        },
    ],
}
