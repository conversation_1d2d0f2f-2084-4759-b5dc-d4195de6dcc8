<script lang="ts" setup>
import aicService from '@/service/aicService'
import type { ICompanyInfo, IGetCompanyByCodeParams } from '@/types/company'
import { mergeList } from '@/utils/merge-list'
import { showFailToast } from 'vant'
import { computed, ref, watch, defineExpose } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import CompanySearchList from '@/components/enterprise/list/CompanySearchList.vue'
import { formatFilters } from '@/utils/enterprise/filters'

const props = defineProps<{
    keyword: string
    scope: string
    externalParams: Record<string, string>
    active?: boolean
    label?: string
    refresh?: number
    reload?: number
    onSearch?: number
}>()

const initial = ref(false)
const loading = ref(false)
const filterParams = ref<{ [x: string]: string | string[] }>({})
const list = ref<ICompanyInfo[]>([])
const searchchannellype = ref(0)
const finished = ref(false)
const error = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 40,
    total: 0,
    realTotal: 0,
})
const store = useStore<RootState>()
const refreshing = ref(false)

const searchParams = computed(() => {
    const { normalFilterParams } = store.state.enterprise

    return normalFilterParams
})

const requestParams = ref<IGetCompanyByCodeParams>({
    keyword: props.keyword,
    matchType: 'most_fields',
    scope: props.scope,
})

const getData = (action?: 'refresh' | 'reload' | 'switch') => {
    if (refreshing.value || loading.value) return

    if (action === 'refresh') {
        refreshing.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
    } else if (action === 'reload') {
        finished.value = false
        loading.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
        list.value = []
    } else {
        finished.value = false
        loading.value = true
    }

    aicService
        .searchEnterprise({
            ...requestParams.value,
            type: 1,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            ...filterParams.value,
        })
        .then((res) => {
            loading.value = false
            refreshing.value = false
            const { errCode, data, total, channelType, realTotal, errMsg, lastPage } = res
            if (errCode === 0) {
                error.value = false
                list.value = mergeList(list.value, data, 'id')
                pageInfo.value.total = total
                pageInfo.value.realTotal = realTotal
                searchchannellype.value = channelType

                if (!lastPage) pageInfo.value.page += 1
                finished.value = list.value.length >= total

                if (total === 0) {
                    list.value = []
                }
            } else {
                error.value = true
                showFailToast(errMsg || '查询失败')
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                refreshing.value = false
                loading.value = false
                error.value = true
            }
        })
        .finally(() => {
            refreshing.value = false
            loading.value = false
            initial.value = true
        })
}

const refreshData = () => {
    pageInfo.value.page = 1
    getData('refresh')
}

const handleSearch = (a?: 'refresh' | 'reload' | 'switch') => {
    if (a === 'switch') {
        if (list.value.length !== 0) return
    }

    getData(a)
}

watch(
    () => props.active,
    (value) => {
        if (value && list.value.length === 0) handleSearch('reload')
    }
)

watch(
    () => props.keyword,
    (value) => {
        console.log('keyword', value)
        requestParams.value = { ...requestParams.value, keyword: value }
    }
)

watch(
    () => searchParams,
    (value) => {
        const formattedData = formatFilters(value.value)
        filterParams.value = formattedData
    },
    { deep: true }
)

export interface ISearchListExpose {
    handleSearch: (action?: 'refresh' | 'reload' | 'switch') => void
}

defineExpose({ handleSearch })
</script>

<template>
    <CompanySearchList
        :load-data="getData"
        :refresh-data="refreshData"
        :loading="loading"
        :refreshing="refreshing"
        :data="list"
        :total="pageInfo.total"
        :finished="finished"
    />
</template>

<style scoped></style>
