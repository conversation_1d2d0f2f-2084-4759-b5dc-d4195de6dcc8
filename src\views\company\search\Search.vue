<script lang="ts" setup>
import commonData from '@/js/common-data'
import { ref, watch, onMounted } from 'vue'
import { SearchHistory, SearchList } from './components'
import { SearchFilter } from '@/components/enterprise/filters'
import { setSearchHistory } from '@/utils/history'
import type { ISearchListExpose } from './components/SearchList.vue'
import { useStore } from 'vuex'

defineOptions({
    name: 'company-search',
})

const store = useStore()
const keyword = ref('')
const input = ref('')
const active = ref(0)
const externalParams = ref<Record<string, string>>({})
const refresh = ref(0)
const reload = ref(0)
const searchListRef = ref<ISearchListExpose[]>()
const showHistory = ref(0) // 0 初始化 1 不展示 2 展示
const showFilterRef = ref(false)

onMounted(() => {
    store.dispatch('app/setNormalSearchRulesData', null)
})

const setKeyword = (value: string) => {
    keyword.value = value
}
const handleSearchBtn = () => {
    setKeyword(input.value)
    onSearch()
}

const onSearch = () => {
    showHistory.value = 1
    setSearchHistory(keyword.value)
    doSearch('reload')
}

const onClear = () => {
    input.value = ''
    keyword.value = ''
}

const onPick = (value: string) => {
    console.log(value)
    input.value = value
    setKeyword(value)
    onSearch()
}

const onFilter = () => {
    showFilterRef.value = true
}

const closeFilter = () => {
    showFilterRef.value = false
}

const onFilterConfirm = () => {
    onSearch()
    closeFilter()
}

const onSearchFocus = () => {
    if (showHistory.value === 0) return
    showHistory.value = 2
}

const onSearchBlur = () => {
    if (showHistory.value === 0) return
    setTimeout(() => {
        showHistory.value = 1
    }, 300)
}

const doSearch = (a?: 'refresh' | 'reload' | 'switch') => {
    setTimeout(() => {
        if (searchListRef.value && searchListRef.value[active.value]) {
            searchListRef.value[active.value].handleSearch(a)
        }
    }, 100)
}

watch(
    () => active.value,
    () => {
        doSearch('switch')
    }
)
</script>

<template>
    <div id="search" class="company-search flex flex-column height-100 oh">
        <van-search
            v-model="input"
            placeholder="请输入搜索关键词"
            :show-action="true"
            @clear="onClear"
            right-icon="search"
            left-icon=""
            @click-right-icon="handleSearchBtn"
            :autofocus="true"
            class="search-input"
            @focus="onSearchFocus"
            @blur="onSearchBlur"
        >
            <template #right-icon>
                <div class="back-color-blue lr-padding-4 color-white border-radius-4">
                    <van-icon name="search" />
                </div>
            </template>

            <template #action>
                <div class="flex flex-column left-right-center top-bottom-center">
                    <div @click="onFilter" class="font-14 color-blue">筛选</div>
                </div>
            </template>
        </van-search>
        <van-tabs v-model:active="active" :sticky="true">
            <van-tab v-for="item in commonData.lxySearchTagTypes" :title="item.label" :key="item.val"></van-tab>
        </van-tabs>
        <div class="flex flex-column height-100 oa" style="background-color: #f2f5f8">
            <template v-for="(item, index) in commonData.lxySearchTagTypes" :key="item.key">
                <div
                    :class="{
                        hide: index !== active || showHistory !== 1,
                        'height-100': true,
                    }"
                >
                    <SearchList
                        ref="searchListRef"
                        :keyword="keyword"
                        :scope="commonData.lxySearchTagTypes[index].val"
                        :externalParams="externalParams"
                        :active="index === active"
                        :label="item.label"
                        :refresh="refresh"
                        :reload="reload"
                    />
                </div>
            </template>
            <template v-if="showHistory !== 1">
                <SearchHistory :onPick="onPick" />
            </template>
        </div>

        <SearchFilter searchType="comSearch" :show="showFilterRef" :close="closeFilter" :confirm="onFilterConfirm" />
    </div>
</template>

<style scoped></style>
