<script lang="ts" setup>
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import type { ISectionItem } from '@/types/company'
import { closeToast, showConfirmDialog, showLoadingToast } from 'vant'
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
    socialCreditCode: string
    name: string
    isBuy: boolean
    updateBuyStatus: () => void
}>()
const router = useRouter()
const active = ref(0)
const isSticky = ref(false)
const sections = ref<(HTMLElement | null)[]>([])
const loading = ref(false)
const list = ref<ISectionItem[]>([])

const onStickyChange = (isFixed: boolean) => {
    isSticky.value = isFixed
}

const getSetcions = () => {
    loading.value = true
    aicService.searchGetSectionNum({ socialCreditCode: props.socialCreditCode }).then((res) => {
        loading.value = false
        list.value = res.data
    })
}

const buyConfirm = () => {
    if (props.isBuy) {
        buyService()
        return
    }

    showConfirmDialog({
        title: '解锁',
        message: `解锁将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            buyService()
        })
        .catch(() => {})
}

const buyService = () => {
    if (!props.socialCreditCode) return

    showLoadingToast({
        message: '解锁中...',
        forbidClick: true,
        loadingType: 'spinner',
    })

    orderService
        .orderBuyLegal({
            companyName: props.name,
            socialCreditCode: props.socialCreditCode,
            serviceKey: 'xs',
        })
        .then((res) => {
            closeToast()
            if (res.contacts && list.value) {
                props.updateBuyStatus()
                // 批量修改数组内值
                list.value.forEach((item) => {
                    item.isLock = 0
                })
            }
        })
}

const toModelPage = (key: string) => {
    router.push({
        name: 'company-model-detail',
        query: {
            socialCreditCode: props.socialCreditCode,
            modelName: key,
        },
    })
}

onMounted(() => {
    getSetcions()
})

watch(
    () => active.value,
    (value) => {
        if (sections.value[value]) {
            sections.value[value].scrollIntoView({
                behavior: 'smooth', // 平滑滚动
                block: value >= 2 ? 'start' : 'center', // 对齐到顶部
            })
        }
    }
)

watch(
    () => props.isBuy,
    (value) => {
        if (value) {
            list.value.forEach((item) => {
                item.isLock = 0
            })
        }
    }
)
</script>
<template>
    <van-skeleton title :row="3" v-if="loading" />
    <div class="flex flex-column gap-12">
        <van-sticky @change="onStickyChange">
            <van-tabs v-model:active="active" class="width-100">
                <van-tab v-for="model in list" :key="model.section" :title="model.sectionName"></van-tab>
            </van-tabs>
        </van-sticky>
        <template v-for="(model, index) in list" :key="model.section">
            <div
                :class="{
                    'back-color-white': true,
                    offset: index === 0 && isSticky,
                }"
                ref="sections"
            >
                <div class="section-title">
                    <div class="font-16 font-weight-500">{{ model.sectionName }}</div>
                </div>
                <div class="flex flex-row flex-wrap width-100 relative">
                    <div
                        class="lock-mask height-100 back-color-second-blue width-100 flex flex-column center gap-8"
                        v-if="model.isLock === 1"
                    >
                        <Icon icon="icon-a-lock" :size="32" color="main-blue" />
                        <div class="font-14 font-weight-500 color-black">使用权益解锁查看</div>
                        <van-button
                            type="primary"
                            size="small"
                            style="height: 28px; font-size: 12px"
                            @click="buyConfirm"
                        >
                            立即查看
                        </van-button>
                    </div>
                    <div
                        class="module-item"
                        v-for="value in model.values"
                        @click="toModelPage(value.label)"
                        :key="value.label"
                    >
                        <div
                            :class="{
                                'module-item-count': true,
                                'color-blue': value.isHighLight,
                                'color-text-grey': !value.isHighLight,
                            }"
                        >
                            {{ value.num }}
                        </div>
                        <div class="module-item-container">
                            <Icon
                                :icon="value.icon"
                                :size="24"
                                class="b-margin-10"
                                :color="`${value.isHighLight ? 'var(--main-blue-)' : 'var(--text-grey)'}`"
                            />
                            <span
                                :class="{
                                    'font-13': true,
                                    'color-black': value.isHighLight,
                                    'color-text-grey': !value.isHighLight,
                                }"
                            >
                                {{ value.labelName }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<style scoped lang="scss">
.company-detail :deep(.van-tabs__nav) {
    background-color: transparent;
}

.company-detail .section-title {
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
}

.company-detail .module-item {
    width: 25%;
    padding: 24px 0 14px 0;
    position: relative;
    border-bottom: 0.6px solid #eee;
}

.company-detail .module-item-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-right: 0.6px solid #eee;
}

.company-detail .module-item:nth-child(4n) .module-item-container {
    border-right: 0;
}

.company-detail .module-item-count {
    position: absolute;
    right: 8px;
    top: 8px;
    font-size: 13px;
}

.company-detail :deep(.van-tab--grow) {
    padding: 0 6px;
}

.company-detail :deep(.van-sticky--fixed) .van-tabs__nav {
    background-color: white;
}

.company-detail .lock-mask {
    position: absolute;
    z-index: 2;
    opacity: 0.95;
}
</style>
