import type { INormalFilterParams } from '@/types/aic'
import type { EnterpriseState, RootState } from '@/types/store'
import { type Module } from 'vuex'

export const originalState: EnterpriseState = {
    normalFilterParams: [],
    removeTarget: null,
    tenderFilterParams: [],
    projectFilterParams: [],
    factoryFilterParams: [],
    lastEntFilterParams: [],
}

const dealReciver = (stateParams: INormalFilterParams[], params: INormalFilterParams | INormalFilterParams[]) => {
    console.log('params1111')
    console.log(params)
    if (Array.isArray(params)) {
        const keySet = new Set<string>()
        params.forEach((item) => {
            const { type, categoryKey } = item || {}
            if (type === 'area') {
                keySet.add(categoryKey)
            }
            if (type === 'mulipleProvince') {
                keySet.add(categoryKey)
            }
            if (type === 'mapped') {
                keySet.add(categoryKey)
            }
        })

        keySet.forEach((key) => {
            stateParams = stateParams.filter((e) => e.categoryKey !== key)
        })

        params.forEach((item) => {
            const { type } = item || {}
            if (type === 'area') {
                console.log('stateParams')
                console.log(stateParams)
                stateParams.push(item)
            }
            if (type === 'mapped') {
                stateParams.push(item)
            }
            if (type === 'mulipleProvince') {
                stateParams.push(item)
            }
        })
    } else {
        const { type, categoryKey, value, checked } = params || {}
        // 单选, 去除的同类型的
        if (type === 'select') {
            stateParams = stateParams.filter((e) => e.categoryKey !== categoryKey)
        }

        // 只有categoryKey, 清除categoryKey所有数据
        if (categoryKey && value !== '' && !value) {
            stateParams = stateParams.filter((e) => e.categoryKey !== categoryKey)
            return stateParams
        }

        if (type === 'select' || type === 'multiSelect') {
            if (checked) {
                stateParams.push(params)
            } else {
                const targetIndex = stateParams.findIndex((e) => e.categoryKey === categoryKey && e.value === value)

                if (targetIndex !== -1) {
                    stateParams.splice(targetIndex, 1)
                }
            }
        }

        // 单个移除mapped值
        if (type === 'mapped') {
            const targetIndex = stateParams.findIndex((e) => e.categoryKey === categoryKey && e.value === value)
            console.log(targetIndex)
            if (targetIndex !== -1) {
                stateParams.splice(targetIndex, 1)
            }
        }

        if (type === 'area') {
            console.log('走到这了')
            const targetIndex = stateParams.findIndex((e) => e.categoryKey === categoryKey && e.value === value)
            if (targetIndex !== -1) {
                stateParams.splice(targetIndex, 1)
            }
        }

        if (type === 'mulipleProvince') {
            console.log('走到这了 mulipleProvince')
            console.log(stateParams)
            const targetIndex = stateParams.findIndex((e) => e.categoryKey === categoryKey && e.value === value)
            if (targetIndex !== -1) {
                stateParams.splice(targetIndex, 1)
            }
        }
    }

    return stateParams
}

const enterpriseModule: Module<EnterpriseState, RootState> = {
    namespaced: true, // 启用命名空间
    state: () => originalState,
    getters: {
        normalFilterParams: (state: EnterpriseState) => state.normalFilterParams,
    },
    mutations: {
        INIT_NORMAL_PARAMS(state: EnterpriseState) {
            console.log('INIT_NORMAL_PARAMS')
            state.normalFilterParams = []
        },
        SET_NORMAL_PARAMS(state: EnterpriseState, params: INormalFilterParams | INormalFilterParams[]) {
            const result = dealReciver(state.normalFilterParams, params)
            if (!result) return
            state.normalFilterParams = result
        },
        INIT_TENDER_PARAMS(state: EnterpriseState) {
            console.log('INIT_TENDER_PARAMS')
            state.tenderFilterParams = []
        },
        SET_TENDER_PARAMS(state: EnterpriseState, params: INormalFilterParams | INormalFilterParams[]) {
            const result = dealReciver(state.tenderFilterParams, params)
            if (!result) return
            state.tenderFilterParams = result
        },
        INIT_PROJECT_PARAMS(state: EnterpriseState) {
            console.log('INIT_PROJECT_PARAMS')
            state.projectFilterParams = []
        },
        SET_PROJECT_PARAMS(state: EnterpriseState, params: INormalFilterParams | INormalFilterParams[]) {
            const result = dealReciver(state.projectFilterParams, params)
            if (!result) return
            state.projectFilterParams = result
        },
        INIT_FACTORY_PARAMS(state: EnterpriseState) {
            console.log('INIT_FACTORY_PARAMS')
            state.factoryFilterParams = []
        },
        SET_FACTORY_PARAMS(state: EnterpriseState, params: INormalFilterParams | INormalFilterParams[]) {
            const result = dealReciver(state.factoryFilterParams, params)
            if (!result) return
            state.factoryFilterParams = result
        },
        INIT_LAT_INT_PARAMS(state: EnterpriseState) {
            console.log('INIT_LAT_INT_PARAMS')
            state.lastEntFilterParams = []
        },
        SET_LAST_ENT_PARAMS(state: EnterpriseState, params: INormalFilterParams | INormalFilterParams[]) {
            const result = dealReciver(state.lastEntFilterParams, params)
            if (!result) return
            state.lastEntFilterParams = result
        },
    },
    actions: {
        setNormalParams({ commit }, params: INormalFilterParams) {
            commit('SET_NORMAL_PARAMS', params)
        },
        initNormalParams({ commit }) {
            commit('INIT_NORMAL_PARAMS')
        },
        setTenderSearchParams({ commit }, params: INormalFilterParams) {
            commit('SET_TENDER_PARAMS', params)
        },
        initTenderParams({ commit }) {
            commit('INIT_TENDER_PARAMS')
        },
        setProjectParams({ commit }, params: INormalFilterParams) {
            commit('SET_PROJECT_PARAMS', params)
        },
        initProjectParams({ commit }) {
            commit('INIT_PROJECT_PARAMS')
        },
        setFactoryParams({ commit }, params: INormalFilterParams) {
            commit('SET_FACTORY_PARAMS', params)
        },
        initFactoryParams({ commit }) {
            commit('INIT_FACTORY_PARAMS')
        },
        setLastEntParams({ commit }, params: INormalFilterParams) {
            commit('SET_LAST_ENT_PARAMS', params)
        },
        initLastEntParams({ commit }) {
            commit('INIT_LAT_INT_PARAMS')
        },
    },
}

export default enterpriseModule
