<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import crmService from '@/service/crmService'
import type { IGoodsProductItem } from '@/types/lead'
import BANKICON from '@/assets/hub-images/company/product-bank.png'

const props = defineProps({
    companyId: {
        type: String,
        required: true,
    },
})
const dataList = ref<IGoodsProductItem[] | null>(null)
const getList = () => {
    crmService.goodsFinanceEntMatchRule({ companyId: props.companyId }).then((res) => {
        console.log('获取产品匹配列表结果', res)
        dataList.value = res.map((item: IGoodsProductItem) => {
            return { ...item, ...item.matchScore, name: item.name }
        })
    })
}
onMounted(() => {
    getList()
})
</script>
<template>
    <van-skeleton title :row="3" v-if="!dataList" />
    <div v-if="dataList?.length === 0" class="font-14">暂无匹配项目</div>
    <div class="flex flex-column width-100">
        <div class="flex flex-column gap-8">
            <div
                class="border-radius-8 border-tag tb-padding-12 lr-padding-16"
                v-for="(item, index) in dataList"
                :key="index"
            >
                <div class="display-flex b-margin-8">
                    <div class="r-margin-8">
                        <div class="w-50 h-50 border-radius-8 border-tag">
                            <img :src="item.bannerImags[0]?.url || BANKICON" alt="" class="width-100" />
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="display-flex space-between top-bottom-center">
                            <div class="font-16 text-overflow">{{ item.name }}</div>
                            <div class="font-14 color-blue">匹配度：{{ item?.totalScore || 0 }}%</div>
                        </div>
                        <div class="flex flex-column font-14 color-text-grey gap-4">
                            <div>
                                最高金额：：<span class="color-red">{{ item.spu.moneyLimits }}</span>
                            </div>
                            <div>
                                年化率：
                                <span class="color-black"> {{ item.spu.rateDown }}-{{ item.spu.rateUpper }}% </span>
                            </div>
                            <div>
                                贷款期限：<span class="color-black">{{ item.spu.loanCycle }}</span>
                            </div>
                            <div class="small-tag tag-blue" v-if="item.sellPoint">
                                {{ item.sellPoint }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped lang="scss"></style>
